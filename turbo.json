{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**", "coverage/**"]}, "dev": {"cache": false, "persistent": true}, "start": {"cache": false, "persistent": true, "dependsOn": ["build"]}, "lint": {"dependsOn": ["^lint"]}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "test:integration": {"dependsOn": ["^build"], "outputs": ["coverage/**", "test/integration/coverage/**"], "cache": true}, "test:watch": {"cache": false, "persistent": true}, "test:integration:watch": {"cache": false, "persistent": true}, "clean": {"cache": false}, "format": {"cache": false}, "db:generate": {"cache": false}, "db:push": {"cache": false}, "db:migrate": {"cache": false}, "db:studio": {"cache": false, "persistent": true}, "db:seed": {"cache": false}, "seed:suppliers": {"cache": false}}, "globalDependencies": ["package.json", "pnpm-workspace.yaml", "turbo.json", ".env*"]}