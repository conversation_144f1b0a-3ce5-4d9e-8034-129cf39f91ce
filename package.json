{"name": "pharmacy-store", "version": "1.0.0", "description": "Pharmacy Store Monorepo with Authentication", "private": true, "workspaces": ["packages/*"], "scripts": {"dev": "bun --bun turbo run dev", "build": "turbo run build", "start": "turbo run start", "lint": "turbo run lint", "test": "turbo run test", "test:integration": "turbo run test:integration", "test:watch": "turbo run test:watch", "test:integration:watch": "turbo run test:integration:watch", "clean": "turbo run clean", "format": "turbo run format", "db:generate": "bun --filter backend db:generate", "db:push": "bun --filter backend db:push", "db:migrate": "bun --filter backend db:migrate", "db:studio": "bun --filter backend db:studio", "db:seed": "bun --filter backend db:seed", "seed:suppliers": "bun --filter backend seed:suppliers"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0", "turbo": "^2.5.4", "typescript": "^5.0.0"}, "packageManager": "bun@1.2.15", "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}}