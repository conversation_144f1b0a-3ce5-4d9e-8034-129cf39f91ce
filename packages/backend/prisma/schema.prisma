// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  ADMIN
  PHARMACIST
  CASHIER
}

enum SupplierType {
  PBF // Pedagang Besar Farmasi
  MANUFACTURER // Pabrik Farmasi
  DISTRIBUTOR // Distributor
  LOCAL // Supplier Lokal
}

enum SupplierStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING
}

enum PaymentMethod {
  CASH
  TRANSFER
  CREDIT
  GIRO
}

enum CustomerType {
  WALK_IN
  REGISTERED
}

enum SaleStatus {
  DRAFT
  COMPLETED
  CANCELLED
  REFUNDED
}

enum DocumentType {
  LICENSE
  CERTIFICATE
  CONTRACT
  TAX_DOCUMENT
  PAYMENT_PROOF
  OTHER
}

enum UnitType {
  WEIGHT // kg, g, mg
  VOLUME // L, ml
  COUNT // pieces, tablets, capsules
  LENGTH // m, cm, mm
  AREA // m2, cm2
  PACKAGE // box, strip, bottle
}

enum ProductType {
  MEDICINE
  MEDICAL_DEVICE
  SUPPLEMENT
  COSMETIC
  GENERAL
}

enum ProductCategory {
  // Medicine Categories
  ANALGESIC // Obat Pereda Nyeri
  ANTIBIOTIC // Antibiotik
  ANTACID // Antasida
  VITAMIN // Vitamin
  SUPPLEMENT // Suplemen
  COUGH_COLD // Obat Batuk Pilek
  DIGESTIVE // Obat Pencernaan
  TOPICAL // Obat Luar
  CARDIOVASCULAR // Obat Jantung
  DIABETES // Obat Diabetes
  HYPERTENSION // Obat Hipertensi

  // Non-Medicine Categories
  MEDICAL_DEVICE // Alat Kesehatan
  COSMETIC // Kosmetik
  BABY_CARE // Perawatan Bayi
  PERSONAL_CARE // Perawatan Pribadi
  FIRST_AID // P3K
  OTHER // Lainnya
}

enum MedicineClassification {
  // Modern Medicine Classifications (Obat Modern)
  OBAT_BEBAS // Green circle - Over-the-counter medicines
  OBAT_BEBAS_TERBATAS // Blue circle - Limited OTC with warnings (P.No. 1-6)
  OBAT_KERAS // Red circle with "K" - Prescription medicines
  NARKOTIKA // White circle with red cross - Controlled narcotics
  PSIKOTROPIKA // Blue circle with red cross - Controlled psychotropic substances

  // Traditional Medicine Classifications (Obat Tradisional)
  JAMU // Green leaf - Traditional herbal remedies
  OBAT_HERBAL_TERSTANDAR // Three green stars - Standardized herbal medicines
  FITOFARMAKA // Green crystal/network - Clinical tested traditional medicines

  // Non-Medicine Items
  NON_MEDICINE // For medical devices, cosmetics, etc.
}

enum StockMovementType {
  IN // Stock received (purchase, return, adjustment)
  OUT // Stock issued (sale, waste, adjustment)
  ADJUSTMENT // Manual stock adjustment
  TRANSFER // Transfer between locations
  ALLOCATION // Stock allocated using FIFO/FEFO
}

model User {
  id          String    @id @default(cuid())
  email       String    @unique
  password    String
  firstName   String
  lastName    String
  avatar      String?
  dateOfBirth DateTime?
  phoneNumber String?
  address     String?
  role        UserRole  @default(CASHIER)
  lastLoginAt DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  createdSuppliers Supplier[] @relation("SupplierCreatedBy")
  updatedSuppliers Supplier[] @relation("SupplierUpdatedBy")
  createdProducts  Product[]  @relation("ProductCreatedBy")
  updatedProducts  Product[]  @relation("ProductUpdatedBy")
  createdCustomers Customer[] @relation("CustomerCreatedBy")
  updatedCustomers Customer[] @relation("CustomerUpdatedBy")
  cashierSales     Sale[]     @relation("SaleCashier")

  @@map("users")
}

model ProductUnit {
  id           String   @id @default(cuid())
  name         String // e.g., "Box", "Strip", "Tablet", "Botol", "ml"
  abbreviation String // e.g., "box", "strip", "tab", "btl", "ml"
  type         UnitType
  isBaseUnit   Boolean  @default(false) // Smallest trackable unit
  description  String?
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  productUnitHierarchies ProductUnitHierarchy[]
  baseUnitProducts       Product[]              @relation("ProductBaseUnit")
  inventoryItems         InventoryItem[]
  SaleItem               SaleItem[]

  @@unique([name, type])
  @@map("product_units")
}

model AppSettings {
  id           String   @id @default(cuid())
  settingKey   String   @unique
  settingValue String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("app_settings")
}

model Supplier {
  id     String         @id @default(cuid())
  code   String         @unique
  name   String
  type   SupplierType
  status SupplierStatus @default(ACTIVE)

  // Contact Information
  address    String?
  city       String?
  province   String?
  postalCode String?
  phone      String?
  email      String?
  website    String?

  // Business Information
  npwp            String? // Tax ID (Nomor Pokok Wajib Pajak)
  licenseNumber   String? // Nomor Izin Usaha
  pharmacyLicense String? // Izin Apotek/PBF

  // Financial Information
  creditLimit      Decimal?       @db.Decimal(15, 2)
  paymentTerms     Int? // Payment terms in days
  preferredPayment PaymentMethod?

  // Bank Information
  bankName        String?
  bankAccount     String?
  bankAccountName String?

  // Metadata
  notes     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String?
  updatedBy String?

  // Relations
  contacts       SupplierContact[]
  documents      SupplierDocument[]
  payments       SupplierPayment[]
  createdByUser  User?              @relation("SupplierCreatedBy", fields: [createdBy], references: [id])
  updatedByUser  User?              @relation("SupplierUpdatedBy", fields: [updatedBy], references: [id])
  inventoryItems InventoryItem[]

  @@map("suppliers")
}

model SupplierContact {
  id         String   @id @default(cuid())
  supplierId String
  name       String
  position   String?
  phone      String?
  email      String?
  isPrimary  Boolean  @default(false)
  isActive   Boolean  @default(true)
  notes      String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  supplier Supplier @relation(fields: [supplierId], references: [id], onDelete: Cascade)

  @@map("supplier_contacts")
}

model SupplierDocument {
  id          String       @id @default(cuid())
  supplierId  String
  paymentId   String? // For PAYMENT_PROOF documents
  type        DocumentType
  name        String
  description String?
  fileName    String?
  filePath    String?
  fileSize    Int?
  mimeType    String?
  expiryDate  DateTime?
  isActive    Boolean      @default(true)
  uploadedAt  DateTime     @default(now())
  uploadedBy  String?

  // Relations
  supplier Supplier         @relation(fields: [supplierId], references: [id], onDelete: Cascade)
  payment  SupplierPayment? @relation(fields: [paymentId], references: [id], onDelete: Cascade)

  @@map("supplier_documents")
}

model SupplierPayment {
  id            String        @id @default(cuid())
  supplierId    String
  invoiceNumber String?
  amount        Decimal       @db.Decimal(15, 2)
  paymentMethod PaymentMethod
  paymentDate   DateTime
  dueDate       DateTime?
  status        String        @default("PENDING") // PENDING, PAID, OVERDUE, CANCELLED
  reference     String?       @unique // Ensure reference numbers are unique
  notes         String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  createdBy     String?

  // Relations
  supplier      Supplier           @relation(fields: [supplierId], references: [id], onDelete: Cascade)
  paymentProofs SupplierDocument[]

  @@map("supplier_payments")
}

model Product {
  id           String          @id @default(cuid())
  code         String          @unique
  name         String
  genericName  String?
  type         ProductType     @default(MEDICINE)
  category     ProductCategory @default(OTHER)
  manufacturer String?

  // Indonesian Pharmacy Specific Fields
  bpomNumber             String? // Indonesian drug registration number
  medicineClassification MedicineClassification @default(NON_MEDICINE) // Indonesian medicine classification
  regulatorySymbol       String? // Official Indonesian regulatory symbol description

  // Unit and Pricing Information
  baseUnitId   String // Smallest trackable unit (e.g., tablet, ml)
  minimumStock Int? // In base units
  maximumStock Int? // In base units
  reorderPoint Int? // In base units

  // Product Information
  description      String?
  activeIngredient String? // Kandungan aktif
  strength         String? // Kekuatan (e.g., "500mg", "10ml")
  dosageForm       String? // Bentuk sediaan (tablet, sirup, kapsul, etc.)
  indication       String? // Indikasi
  contraindication String? // Kontraindikasi
  sideEffects      String? // Efek samping
  dosage           String? // Dosis
  storage          String? // Penyimpanan

  // Metadata
  notes     String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String?
  updatedBy String?

  // Relations
  baseUnit        ProductUnit            @relation("ProductBaseUnit", fields: [baseUnitId], references: [id])
  unitHierarchies ProductUnitHierarchy[]
  createdByUser   User?                  @relation("ProductCreatedBy", fields: [createdBy], references: [id])
  updatedByUser   User?                  @relation("ProductUpdatedBy", fields: [updatedBy], references: [id])
  inventoryItems  InventoryItem[]
  SaleItem        SaleItem[]

  @@map("products")
}

model ProductUnitHierarchy {
  id               String   @id @default(cuid())
  productId        String
  unitId           String
  parentUnitId     String? // NULL for top-level unit
  conversionFactor Decimal  @db.Decimal(10, 4) // How many of this unit = 1 parent unit
  level            Int // 0 = base unit, 1 = next level up, etc.
  sellingPrice     Decimal? @db.Decimal(15, 2) // Price per this unit
  costPrice        Decimal? @db.Decimal(15, 2) // Cost per this unit
  isActive         Boolean  @default(true)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  product    Product                @relation(fields: [productId], references: [id], onDelete: Cascade)
  unit       ProductUnit            @relation(fields: [unitId], references: [id])
  parentUnit ProductUnitHierarchy?  @relation("UnitHierarchy", fields: [parentUnitId], references: [id])
  childUnits ProductUnitHierarchy[] @relation("UnitHierarchy")

  @@unique([productId, unitId])
  @@map("product_unit_hierarchies")
}

model InventoryItem {
  id                String    @id @default(cuid())
  productId         String
  unitId            String // Which unit this inventory is tracked in
  batchNumber       String? // Batch/lot number for tracking
  expiryDate        DateTime? // Expiry date for medicines
  quantityOnHand    Int // Physical quantity in the specified unit (actual stock)
  quantityAllocated Int       @default(0) // Quantity allocated/reserved but not yet consumed
  costPrice         Decimal   @db.Decimal(15, 2) // Cost per unit
  sellingPrice      Decimal?  @db.Decimal(15, 2) // Selling price per unit
  location          String? // Storage location (e.g., "Rak A-1", "Gudang")

  // FIFO tracking
  receivedDate DateTime @default(now()) // When this batch was received
  supplierId   String? // Which supplier provided this batch

  // Status and metadata
  isActive  Boolean  @default(true)
  notes     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String?
  updatedBy String?

  // Relations
  product        Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
  unit           ProductUnit     @relation(fields: [unitId], references: [id])
  supplier       Supplier?       @relation(fields: [supplierId], references: [id])
  stockMovements StockMovement[]

  @@map("inventory_items")
}

model StockMovement {
  id              String            @id @default(cuid())
  inventoryItemId String
  type            StockMovementType
  quantity        Int // Quantity moved (positive for IN, negative for OUT)
  unitPrice       Decimal?          @db.Decimal(15, 2) // Price per unit for this movement

  // Reference information
  referenceType   String? // e.g., "PURCHASE", "SALE", "ADJUSTMENT", "TRANSFER"
  referenceId     String? // ID of the related transaction
  referenceNumber String? // Human-readable reference (e.g., invoice number)

  // Movement details
  reason       String? // Reason for the movement
  notes        String?
  movementDate DateTime @default(now())

  // Metadata
  createdAt DateTime @default(now())
  createdBy String?

  // Relations
  inventoryItem InventoryItem @relation(fields: [inventoryItemId], references: [id], onDelete: Cascade)

  @@map("stock_movements")
}

model Customer {
  id   String       @id @default(cuid())
  code String       @unique // Auto-generated customer code
  type CustomerType @default(WALK_IN)

  // Personal Information
  firstName   String?
  lastName    String?
  fullName    String // For walk-in customers or combined name
  phoneNumber String?
  email       String?
  dateOfBirth DateTime?
  gender      String? // M/F

  // Address Information
  address    String?
  city       String?
  province   String?
  postalCode String?

  // Membership Information
  membershipNumber String? @unique
  membershipLevel  String? // BRONZE, SILVER, GOLD, PLATINUM
  loyaltyPoints    Int     @default(0)

  // Metadata
  notes     String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String?
  updatedBy String?

  // Relations
  createdByUser User?  @relation("CustomerCreatedBy", fields: [createdBy], references: [id])
  updatedByUser User?  @relation("CustomerUpdatedBy", fields: [updatedBy], references: [id])
  sales         Sale[]

  @@map("customers")
}

model Sale {
  id         String     @id @default(cuid())
  saleNumber String     @unique // Auto-generated sale number (e.g., "TRX-20240101-001")
  customerId String? // Optional for walk-in customers
  cashierId  String // Required - who processed the sale
  status     SaleStatus @default(DRAFT)

  // Sale Information
  saleDate DateTime @default(now())

  // Financial Information
  subtotal       Decimal  @default(0) @db.Decimal(15, 2) // Before tax and discount
  discountType   String? // PERCENTAGE, FIXED_AMOUNT
  discountValue  Decimal? @db.Decimal(15, 2) // Discount amount or percentage
  discountAmount Decimal  @default(0) @db.Decimal(15, 2) // Calculated discount amount
  taxAmount      Decimal  @default(0) @db.Decimal(15, 2) // PPN if applicable
  totalAmount    Decimal  @default(0) @db.Decimal(15, 2) // Final amount

  // Payment Information
  paymentMethod PaymentMethod @default(CASH)
  amountPaid    Decimal       @default(0) @db.Decimal(15, 2)
  changeAmount  Decimal       @default(0) @db.Decimal(15, 2)

  // Customer Information (for walk-in customers)
  customerName  String? // Name for walk-in customers
  customerPhone String? // Phone for walk-in customers

  // Metadata
  notes     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  customer  Customer?  @relation(fields: [customerId], references: [id])
  cashier   User       @relation("SaleCashier", fields: [cashierId], references: [id])
  saleItems SaleItem[]

  @@map("sales")
}

model SaleItem {
  id        String @id @default(cuid())
  saleId    String
  productId String
  unitId    String // Unit in which the product was sold

  // Quantity and Pricing
  quantity   Int // Quantity sold in the specified unit
  unitPrice  Decimal @db.Decimal(15, 2) // Price per unit
  totalPrice Decimal @db.Decimal(15, 2) // quantity * unitPrice

  // Discount Information (item-level)
  discountType   String? // PERCENTAGE, FIXED_AMOUNT
  discountValue  Decimal? @db.Decimal(15, 2) // Discount amount or percentage
  discountAmount Decimal  @default(0) @db.Decimal(15, 2) // Calculated discount amount

  // Inventory Tracking
  batchNumber String? // Which batch was used for this sale
  expiryDate  DateTime? // Expiry date of the batch used

  // Metadata
  notes     String?
  createdAt DateTime @default(now())

  // Relations
  sale    Sale        @relation(fields: [saleId], references: [id], onDelete: Cascade)
  product Product     @relation(fields: [productId], references: [id])
  unit    ProductUnit @relation(fields: [unitId], references: [id])

  @@map("sale_items")
}
