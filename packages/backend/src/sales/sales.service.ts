import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateSaleDto } from './dto/create-sale.dto';
import { UpdateSaleDto } from './dto/update-sale.dto';
import { SaleQueryDto } from './dto/sale-query.dto';
import { SaleNumberGeneratorService } from './sale-number-generator.service';
import { InventoryService } from '../inventory/inventory.service';
import { ProductsService } from '../products/products.service';
import { SaleStatus } from '@prisma/client';

@Injectable()
export class SalesService {
  constructor(
    private prisma: PrismaService,
    private saleNumberGeneratorService: SaleNumberGeneratorService,
    private inventoryService: InventoryService,
    private productsService: ProductsService,
  ) { }

  async create(createSaleDto: CreateSaleDto) {
    // Ensure cashierId is provided
    if (!createSaleDto.cashierId) {
      throw new BadRequestException('Cashier ID is required');
    }
    // Generate sale number if not provided
    if (!createSaleDto.saleNumber) {
      createSaleDto.saleNumber = await this.saleNumberGeneratorService.generateSaleNumber();
    } else {
      // Validate sale number uniqueness if provided
      const isUnique = await this.saleNumberGeneratorService.validateSaleNumberUniqueness(
        createSaleDto.saleNumber,
      );
      if (!isUnique) {
        throw new BadRequestException('Nomor transaksi sudah digunakan');
      }
    }

    // Validate items and calculate totals
    let subtotal = 0;
    const processedItems: Array<{
      productId: string;
      unitId: string;
      quantity: number;
      unitPrice: number;
      totalPrice: number;
      discountType?: string;
      discountValue?: number;
      discountAmount: number;
      notes?: string;
    }> = [];

    for (const item of createSaleDto.items) {
      // Validate product exists
      const product = await this.productsService.findOne(item.productId);

      // Check stock availability before processing
      const availableStock = await this.inventoryService.getAvailableStock(item.productId, false);
      if (availableStock < item.quantity) {
        throw new BadRequestException(`Stok tidak mencukupi untuk ${product.name}`);
      }

      // Calculate item total
      const itemTotal = item.quantity * item.unitPrice;

      // Calculate item discount
      let itemDiscountAmount = 0;
      if (item.discountType && item.discountValue) {
        if (item.discountType === 'PERCENTAGE') {
          itemDiscountAmount = (itemTotal * item.discountValue) / 100;
        } else if (item.discountType === 'FIXED_AMOUNT') {
          itemDiscountAmount = item.discountValue;
        }
      }

      const finalItemTotal = itemTotal - itemDiscountAmount;
      subtotal += finalItemTotal;

      processedItems.push({
        productId: item.productId,
        unitId: item.unitId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: itemTotal,
        discountType: item.discountType,
        discountValue: item.discountValue,
        discountAmount: itemDiscountAmount,
        notes: item.notes,
      });
    }

    // Calculate sale-level discount
    let saleDiscountAmount = 0;
    if (createSaleDto.discountType && createSaleDto.discountValue) {
      if (createSaleDto.discountType === 'PERCENTAGE') {
        saleDiscountAmount = (subtotal * createSaleDto.discountValue) / 100;
      } else if (createSaleDto.discountType === 'FIXED_AMOUNT') {
        saleDiscountAmount = createSaleDto.discountValue;
      }
    }

    const afterDiscount = subtotal - saleDiscountAmount;
    const taxAmount = createSaleDto.taxAmount || 0;
    const totalAmount = afterDiscount + taxAmount;

    // For API response, subtotal should be the discounted amount
    const finalSubtotal = afterDiscount;

    // Calculate change
    const changeAmount = createSaleDto.amountPaid - totalAmount;
    if (changeAmount < 0) {
      throw new BadRequestException('Jumlah pembayaran tidak mencukupi');
    }

    // Create sale in transaction
    return this.prisma.$transaction(async (tx) => {
      // Create the sale
      const sale = await tx.sale.create({
        data: {
          saleNumber: createSaleDto.saleNumber!,
          customerId: createSaleDto.customerId || null,
          cashierId: createSaleDto.cashierId!,
          saleDate: createSaleDto.saleDate ? new Date(createSaleDto.saleDate) : new Date(),
          subtotal: finalSubtotal,
          discountType: createSaleDto.discountType,
          discountValue: createSaleDto.discountValue,
          discountAmount: saleDiscountAmount,
          taxAmount,
          totalAmount,
          paymentMethod: createSaleDto.paymentMethod,
          amountPaid: createSaleDto.amountPaid,
          changeAmount,
          customerName: createSaleDto.customerName,
          customerPhone: createSaleDto.customerPhone,
          notes: createSaleDto.notes,
          status: 'COMPLETED', // Mark as completed immediately for basic POS
        },
      });

      // Create sale items and allocate stock
      for (const item of processedItems) {
        // Create sale item
        await tx.saleItem.create({
          data: {
            saleId: sale.id,
            productId: item.productId,
            unitId: item.unitId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            totalPrice: item.totalPrice,
            discountType: item.discountType,
            discountValue: item.discountValue,
            discountAmount: item.discountAmount,
            notes: item.notes,
          },
        });

        // Allocate stock using FIFO/FEFO
        const product = await this.productsService.findOne(item.productId);
        const method = product.type === 'MEDICINE' ? 'FEFO' : 'FIFO';

        const allocation = await this.inventoryService.allocateStock(
          item.productId,
          item.quantity,
          method,
          {
            allowPartialAllocation: false, // Sales require exact quantity
            userId: createSaleDto.cashierId,
            reason: `Penjualan #${sale.saleNumber}`,
            notes: `Pelanggan: ${createSaleDto.customerName || 'Walk-in'}`,
          },
        );

        if (!allocation.success) {
          throw new BadRequestException(`Stok tidak mencukupi untuk ${product.name}`);
        }
      }

      // Convert Decimal fields to numbers for API response
      return {
        ...sale,
        subtotal: Number(sale.subtotal),
        discountAmount: Number(sale.discountAmount),
        taxAmount: Number(sale.taxAmount),
        totalAmount: Number(sale.totalAmount),
        amountPaid: Number(sale.amountPaid),
        changeAmount: Number(sale.changeAmount),
        discountValue: sale.discountValue ? Number(sale.discountValue) : null,
      };
    });
  }

  async findAll(query: SaleQueryDto) {
    const { page = 1, limit = 10, search, status, paymentMethod, customerId, cashierId, startDate, endDate, sortBy = 'createdAt', sortOrder = 'desc' } = query;
    const skip = (page - 1) * limit;

    const where: any = {};

    if (search) {
      where.OR = [
        { saleNumber: { contains: search, mode: 'insensitive' } },
        { customerName: { contains: search, mode: 'insensitive' } },
        { customerPhone: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (status) {
      where.status = status;
    }

    if (paymentMethod) {
      where.paymentMethod = paymentMethod;
    }

    if (customerId) {
      where.customerId = customerId;
    }

    if (cashierId) {
      where.cashierId = cashierId;
    }

    if (startDate || endDate) {
      where.saleDate = {};
      if (startDate) {
        where.saleDate.gte = new Date(startDate);
      }
      if (endDate) {
        where.saleDate.lte = new Date(endDate);
      }
    }

    const [sales, total] = await Promise.all([
      this.prisma.sale.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          [sortBy as string]: sortOrder,
        },
        include: {
          customer: {
            select: {
              id: true,
              code: true,
              fullName: true,
              phoneNumber: true,
              type: true,
            },
          },
          cashier: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          saleItems: {
            include: {
              product: {
                select: {
                  id: true,
                  code: true,
                  name: true,
                  type: true,
                },
              },
              unit: {
                select: {
                  id: true,
                  name: true,
                  abbreviation: true,
                },
              },
            },
          },
        },
      }),
      this.prisma.sale.count({ where }),
    ]);

    return {
      data: sales,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPreviousPage: page > 1,
      },
    };
  }

  async findOne(id: string) {
    const sale = await this.prisma.sale.findUnique({
      where: { id },
      include: {
        customer: {
          select: {
            id: true,
            code: true,
            fullName: true,
            phoneNumber: true,
            email: true,
            type: true,
          },
        },
        cashier: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        saleItems: {
          include: {
            product: {
              select: {
                id: true,
                code: true,
                name: true,
                type: true,
                manufacturer: true,
              },
            },
            unit: {
              select: {
                id: true,
                name: true,
                abbreviation: true,
              },
            },
          },
        },
      },
    });

    if (!sale) {
      throw new NotFoundException('Transaksi tidak ditemukan');
    }

    // Convert Decimal fields to numbers for API response
    return {
      ...sale,
      subtotal: Number(sale.subtotal),
      discountValue: sale.discountValue ? Number(sale.discountValue) : null,
      discountAmount: Number(sale.discountAmount),
      taxAmount: Number(sale.taxAmount),
      totalAmount: Number(sale.totalAmount),
      amountPaid: Number(sale.amountPaid),
      changeAmount: Number(sale.changeAmount),
      saleItems: sale.saleItems.map(item => ({
        ...item,
        unitPrice: Number(item.unitPrice),
        totalPrice: Number(item.totalPrice),
        discountValue: item.discountValue ? Number(item.discountValue) : null,
        discountAmount: Number(item.discountAmount),
      })),
    };
  }

  async getStats() {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    const [totalSales, todaySales, completedSales, totalRevenue, todayRevenue] = await Promise.all([
      this.prisma.sale.count(),
      this.prisma.sale.count({
        where: {
          saleDate: {
            gte: startOfDay,
            lt: endOfDay,
          },
        },
      }),
      this.prisma.sale.count({ where: { status: 'COMPLETED' } }),
      this.prisma.sale.aggregate({
        _sum: {
          totalAmount: true,
        },
        where: {
          status: 'COMPLETED',
        },
      }),
      this.prisma.sale.aggregate({
        _sum: {
          totalAmount: true,
        },
        where: {
          status: 'COMPLETED',
          saleDate: {
            gte: startOfDay,
            lt: endOfDay,
          },
        },
      }),
    ]);

    return {
      totalSales,
      todaySales,
      completedSales,
      totalRevenue: Number(totalRevenue._sum.totalAmount) || 0,
      todayRevenue: Number(todayRevenue._sum.totalAmount) || 0,
    };
  }

  // Enhanced Transaction Processing Methods

  /**
   * Create a draft sale without stock allocation
   * Useful for building transactions before final confirmation
   */
  async createDraft(createSaleDto: CreateSaleDto) {
    // Ensure cashierId is provided
    if (!createSaleDto.cashierId) {
      throw new BadRequestException('Cashier ID is required');
    }

    // Generate sale number if not provided
    if (!createSaleDto.saleNumber) {
      createSaleDto.saleNumber = await this.saleNumberGeneratorService.generateSaleNumber();
    } else {
      // Validate sale number uniqueness if provided
      const isUnique = await this.saleNumberGeneratorService.validateSaleNumberUniqueness(
        createSaleDto.saleNumber,
      );
      if (!isUnique) {
        throw new BadRequestException('Nomor transaksi sudah digunakan');
      }
    }

    // Validate items and calculate totals (without stock allocation)
    let subtotal = 0;
    const processedItems: Array<{
      productId: string;
      unitId: string;
      quantity: number;
      unitPrice: number;
      totalPrice: number;
      discountType?: string;
      discountValue?: number;
      discountAmount: number;
      notes?: string;
    }> = [];

    for (const item of createSaleDto.items) {
      // Validate product exists
      await this.productsService.findOne(item.productId);

      // For draft sales, we skip stock validation entirely
      // Stock will be validated only when completing the sale

      // Calculate item totals
      const itemSubtotal = item.quantity * item.unitPrice;
      let itemDiscountAmount = 0;

      if (item.discountType && item.discountValue) {
        if (item.discountType === 'PERCENTAGE') {
          itemDiscountAmount = (itemSubtotal * item.discountValue) / 100;
        } else if (item.discountType === 'FIXED') {
          itemDiscountAmount = item.discountValue;
        }
      }

      const itemTotal = itemSubtotal - itemDiscountAmount;
      subtotal += itemTotal;

      processedItems.push({
        productId: item.productId,
        unitId: item.unitId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: itemTotal,
        discountType: item.discountType,
        discountValue: item.discountValue,
        discountAmount: itemDiscountAmount,
        notes: item.notes,
      });
    }

    // Calculate sale-level discount
    let saleDiscountAmount = 0;
    if (createSaleDto.discountType && createSaleDto.discountValue) {
      if (createSaleDto.discountType === 'PERCENTAGE') {
        saleDiscountAmount = (subtotal * createSaleDto.discountValue) / 100;
      } else if (createSaleDto.discountType === 'FIXED') {
        saleDiscountAmount = createSaleDto.discountValue;
      }
    }

    const afterDiscount = subtotal - saleDiscountAmount;
    const taxAmount = createSaleDto.taxAmount || 0;
    const totalAmount = afterDiscount + taxAmount;

    // subtotal should remain the raw total before sale-level discounts
    // totalAmount is the final amount after all discounts and taxes

    // Calculate change (for draft sales, we allow insufficient payment)
    const changeAmount = createSaleDto.amountPaid - totalAmount;
    // Note: For draft sales, we don't validate payment sufficiency
    // Payment validation will be done when completing the sale

    // Create draft sale in transaction
    return this.prisma.$transaction(async (tx) => {
      // Create the sale as DRAFT
      const sale = await tx.sale.create({
        data: {
          saleNumber: createSaleDto.saleNumber!,
          customerId: createSaleDto.customerId || null,
          cashierId: createSaleDto.cashierId!,
          saleDate: createSaleDto.saleDate ? new Date(createSaleDto.saleDate) : new Date(),
          subtotal: subtotal,
          discountType: createSaleDto.discountType,
          discountValue: createSaleDto.discountValue,
          discountAmount: saleDiscountAmount,
          taxAmount,
          totalAmount,
          paymentMethod: createSaleDto.paymentMethod,
          amountPaid: createSaleDto.amountPaid,
          changeAmount,
          customerName: createSaleDto.customerName,
          customerPhone: createSaleDto.customerPhone,
          notes: createSaleDto.notes,
          status: SaleStatus.DRAFT, // Keep as draft
        },
      });

      // Create sale items (without stock allocation)
      for (const item of processedItems) {
        await tx.saleItem.create({
          data: {
            saleId: sale.id,
            productId: item.productId,
            unitId: item.unitId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            totalPrice: item.totalPrice,
            discountType: item.discountType,
            discountValue: item.discountValue,
            discountAmount: item.discountAmount,
            notes: item.notes,
          },
        });
      }

      // Fetch the created sale with all relations using the transaction client
      const createdSale = await tx.sale.findUnique({
        where: { id: sale.id },
        include: {
          customer: {
            select: {
              id: true,
              code: true,
              fullName: true,
              phoneNumber: true,
              email: true,
              type: true,
            },
          },
          cashier: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          saleItems: {
            include: {
              product: {
                select: {
                  id: true,
                  code: true,
                  name: true,
                  type: true,
                  manufacturer: true,
                },
              },
              unit: {
                select: {
                  id: true,
                  name: true,
                  abbreviation: true,
                },
              },
            },
          },
        },
      });

      // Convert Decimal fields to numbers for API response
      if (!createdSale) {
        throw new Error('Failed to create sale');
      }

      return {
        ...createdSale,
        subtotal: Number(createdSale.subtotal),
        discountValue: createdSale.discountValue ? Number(createdSale.discountValue) : null,
        discountAmount: Number(createdSale.discountAmount),
        taxAmount: Number(createdSale.taxAmount),
        totalAmount: Number(createdSale.totalAmount),
        amountPaid: Number(createdSale.amountPaid),
        changeAmount: Number(createdSale.changeAmount),
        saleItems: createdSale.saleItems.map(item => ({
          ...item,
          unitPrice: Number(item.unitPrice),
          totalPrice: Number(item.totalPrice),
          discountValue: item.discountValue ? Number(item.discountValue) : null,
          discountAmount: Number(item.discountAmount),
        })),
      };
    });
  }

  /**
   * Complete a draft sale by allocating stock and changing status to COMPLETED
   */
  async completeSale(saleId: string, userId: string) {
    const sale = await this.findOne(saleId);

    if (sale.status !== SaleStatus.DRAFT) {
      throw new ConflictException('Hanya transaksi draft yang dapat diselesaikan');
    }

    // Validate stock availability for all items
    for (const item of sale.saleItems) {
      const availableStock = await this.inventoryService.getAvailableStock(item.productId, false);
      if (availableStock < item.quantity) {
        throw new BadRequestException(`Stok tidak mencukupi untuk ${item.product.name}. Tersedia: ${availableStock}, Dibutuhkan: ${item.quantity}`);
      }
    }

    // Complete the sale in transaction
    return this.prisma.$transaction(async (tx) => {
      // Allocate stock for all items
      for (const item of sale.saleItems) {
        const product = await this.productsService.findOne(item.productId);
        const method = product.type === 'MEDICINE' ? 'FEFO' : 'FIFO';

        const allocation = await this.inventoryService.allocateStock(
          item.productId,
          item.quantity,
          method,
          {
            allowPartialAllocation: false, // Sales require exact quantity
            userId: userId,
            reason: `Penjualan #${sale.saleNumber}`,
            notes: `Pelanggan: ${sale.customerName || 'Walk-in'}`,
          },
        );

        if (!allocation.success) {
          throw new BadRequestException(`Gagal mengalokasi stok untuk ${product.name}`);
        }
      }

      // Update sale status to COMPLETED
      const completedSale = await tx.sale.update({
        where: { id: saleId },
        data: {
          status: SaleStatus.COMPLETED,
          updatedAt: new Date(),
        },
      });

      // Fetch the completed sale with all relations using the transaction client
      return tx.sale.findUnique({
        where: { id: completedSale.id },
        include: {
          customer: {
            select: {
              id: true,
              code: true,
              fullName: true,
              phoneNumber: true,
              email: true,
              type: true,
            },
          },
          cashier: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          saleItems: {
            include: {
              product: {
                select: {
                  id: true,
                  code: true,
                  name: true,
                  type: true,
                  manufacturer: true,
                },
              },
              unit: {
                select: {
                  id: true,
                  name: true,
                  abbreviation: true,
                },
              },
            },
          },
        },
      });
    });
  }

  /**
   * Cancel a sale and restore stock if it was allocated
   */
  async cancelSale(saleId: string, userId: string, reason?: string) {
    const sale = await this.findOne(saleId);

    if (sale.status === SaleStatus.CANCELLED) {
      throw new ConflictException('Transaksi sudah dibatalkan');
    }

    if (sale.status === SaleStatus.REFUNDED) {
      throw new ConflictException('Transaksi yang sudah di-refund tidak dapat dibatalkan');
    }

    return this.prisma.$transaction(async (tx) => {
      // If sale was completed, we need to restore stock
      if (sale.status === SaleStatus.COMPLETED) {
        for (const item of sale.saleItems) {
          // Restore stock by deallocating the quantity that was allocated during sale completion
          await this.inventoryService.deallocateStock(
            item.productId,
            item.quantity,
            {
              referenceType: 'SALE_CANCELLATION',
              referenceId: saleId,
              referenceNumber: sale.saleNumber,
              reason: `Pembatalan transaksi: ${reason || 'Tidak ada alasan'}`,
              userId: userId || 'system',
            }
          );
        }
      }

      // Update sale status to CANCELLED
      const cancelledSale = await tx.sale.update({
        where: { id: saleId },
        data: {
          status: SaleStatus.CANCELLED,
          notes: reason ? `${sale.notes || ''}\nDibatalkan: ${reason}`.trim() : sale.notes,
          updatedAt: new Date(),
        },
      });

      // Fetch the cancelled sale with all relations using the transaction client
      return tx.sale.findUnique({
        where: { id: cancelledSale.id },
        include: {
          customer: {
            select: {
              id: true,
              code: true,
              fullName: true,
              phoneNumber: true,
              email: true,
              type: true,
            },
          },
          cashier: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          saleItems: {
            include: {
              product: {
                select: {
                  id: true,
                  code: true,
                  name: true,
                  type: true,
                  manufacturer: true,
                },
              },
              unit: {
                select: {
                  id: true,
                  name: true,
                  abbreviation: true,
                },
              },
            },
          },
        },
      });
    });
  }

  /**
   * Process a refund for a completed sale
   */
  async refundSale(saleId: string, userId: string, reason?: string) {
    const sale = await this.findOne(saleId);

    if (sale.status !== SaleStatus.COMPLETED) {
      throw new ConflictException('Hanya transaksi yang sudah selesai yang dapat di-refund');
    }

    return this.prisma.$transaction(async (tx) => {
      // Restore stock for all items
      for (const item of sale.saleItems) {
        // Restore stock by deallocating the quantity that was allocated during sale completion
        await this.inventoryService.deallocateStock(
          item.productId,
          item.quantity,
          {
            referenceType: 'SALE_REFUND',
            referenceId: saleId,
            referenceNumber: sale.saleNumber,
            reason: `Refund transaksi: ${reason || 'Tidak ada alasan'}`,
            userId: userId || 'system',
          }
        );
      }

      // Update sale status to REFUNDED
      const refundedSale = await tx.sale.update({
        where: { id: saleId },
        data: {
          status: SaleStatus.REFUNDED,
          notes: reason ? `${sale.notes || ''}\nDi-refund: ${reason}`.trim() : sale.notes,
          updatedAt: new Date(),
        },
      });

      // Fetch the refunded sale with all relations using the transaction client
      return tx.sale.findUnique({
        where: { id: refundedSale.id },
        include: {
          customer: {
            select: {
              id: true,
              code: true,
              fullName: true,
              phoneNumber: true,
              email: true,
              type: true,
            },
          },
          cashier: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          saleItems: {
            include: {
              product: {
                select: {
                  id: true,
                  code: true,
                  name: true,
                  type: true,
                  manufacturer: true,
                },
              },
              unit: {
                select: {
                  id: true,
                  name: true,
                  abbreviation: true,
                },
              },
            },
          },
        },
      });
    });
  }

  /**
   * Update a draft sale with full support for item updates and total recalculation
   */
  async updateDraft(saleId: string, updateSaleDto: UpdateSaleDto) {
    const sale = await this.findOne(saleId);

    if (sale.status !== SaleStatus.DRAFT) {
      throw new ConflictException('Hanya transaksi draft yang dapat diubah');
    }

    // If items are being updated, we need to recalculate totals
    if (updateSaleDto.items) {
      return this.updateDraftWithItems(saleId, updateSaleDto);
    }

    // If only sale-level fields are being updated, use simple update
    return this.updateDraftSaleFields(saleId, updateSaleDto);
  }

  /**
   * Update draft sale with item changes and full recalculation
   */
  private async updateDraftWithItems(saleId: string, updateSaleDto: UpdateSaleDto) {
    // Validate items array
    if (!updateSaleDto.items || updateSaleDto.items.length === 0) {
      throw new BadRequestException('Daftar item tidak boleh kosong');
    }

    // Process and validate items (same logic as createDraft)
    const processedItems: Array<{
      productId: string;
      unitId: string;
      quantity: number;
      unitPrice: number;
      totalPrice: number;
      discountType?: string;
      discountValue?: number;
      discountAmount: number;
      notes?: string;
    }> = [];
    let subtotal = 0;

    for (const item of updateSaleDto.items) {
      // Validate product exists
      const product = await this.productsService.findOne(item.productId);

      // For draft sales, we skip stock validation entirely but can show warnings
      // Stock will be validated only when completing the sale
      const availableStock = await this.inventoryService.getAvailableStock(item.productId, false);
      if (availableStock < item.quantity) {
        // Warning only for draft - don't throw error
        console.warn(`Warning: Insufficient stock for ${product.name}. Available: ${availableStock}, Requested: ${item.quantity}`);
      }

      // Calculate item totals
      const itemSubtotal = item.quantity * item.unitPrice;
      let itemDiscountAmount = 0;

      if (item.discountType && item.discountValue) {
        if (item.discountType === 'PERCENTAGE') {
          itemDiscountAmount = (itemSubtotal * item.discountValue) / 100;
        } else if (item.discountType === 'FIXED') {
          itemDiscountAmount = item.discountValue;
        }
      }

      const itemTotal = itemSubtotal - itemDiscountAmount;
      subtotal += itemTotal;

      processedItems.push({
        productId: item.productId,
        unitId: item.unitId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: itemTotal,
        discountType: item.discountType,
        discountValue: item.discountValue,
        discountAmount: itemDiscountAmount,
        notes: item.notes,
      });
    }

    // Calculate sale-level discount
    let saleDiscountAmount = 0;
    if (updateSaleDto.discountType && updateSaleDto.discountValue) {
      if (updateSaleDto.discountType === 'PERCENTAGE') {
        saleDiscountAmount = (subtotal * updateSaleDto.discountValue) / 100;
      } else if (updateSaleDto.discountType === 'FIXED') {
        saleDiscountAmount = updateSaleDto.discountValue;
      }
    }

    const afterDiscount = subtotal - saleDiscountAmount;
    const taxAmount = updateSaleDto.taxAmount || 0;
    const totalAmount = afterDiscount + taxAmount;

    // Calculate change (for draft sales, we allow insufficient payment)
    const amountPaid = updateSaleDto.amountPaid || 0;
    const changeAmount = amountPaid - totalAmount;

    // Update draft sale in transaction
    return this.prisma.$transaction(async (tx) => {
      // Delete existing sale items
      await tx.saleItem.deleteMany({
        where: { saleId },
      });

      // Update the sale with new totals
      const updatedSale = await tx.sale.update({
        where: { id: saleId },
        data: {
          customerId: updateSaleDto.customerId || null,
          subtotal: subtotal,
          discountType: updateSaleDto.discountType,
          discountValue: updateSaleDto.discountValue,
          discountAmount: saleDiscountAmount,
          taxAmount,
          totalAmount,
          paymentMethod: updateSaleDto.paymentMethod,
          amountPaid,
          changeAmount,
          customerName: updateSaleDto.customerName,
          customerPhone: updateSaleDto.customerPhone,
          notes: updateSaleDto.notes,
          updatedAt: new Date(),
        },
      });

      // Create new sale items
      for (const item of processedItems) {
        await tx.saleItem.create({
          data: {
            saleId: updatedSale.id,
            productId: item.productId,
            unitId: item.unitId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            totalPrice: item.totalPrice,
            discountType: item.discountType,
            discountValue: item.discountValue,
            discountAmount: item.discountAmount,
            notes: item.notes,
          },
        });
      }

      // Fetch the updated sale with all relations
      const updatedSaleWithRelations = await tx.sale.findUnique({
        where: { id: updatedSale.id },
        include: {
          customer: {
            select: {
              id: true,
              code: true,
              fullName: true,
              phoneNumber: true,
              email: true,
              type: true,
            },
          },
          cashier: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          saleItems: {
            include: {
              product: {
                select: {
                  id: true,
                  code: true,
                  name: true,
                  type: true,
                  manufacturer: true,
                },
              },
              unit: {
                select: {
                  id: true,
                  name: true,
                  abbreviation: true,
                },
              },
            },
          },
        },
      });

      if (!updatedSaleWithRelations) {
        throw new Error('Failed to update sale');
      }

      // Convert Decimal fields to numbers for API response
      return {
        ...updatedSaleWithRelations,
        subtotal: Number(updatedSaleWithRelations.subtotal),
        discountValue: updatedSaleWithRelations.discountValue ? Number(updatedSaleWithRelations.discountValue) : null,
        discountAmount: Number(updatedSaleWithRelations.discountAmount),
        taxAmount: Number(updatedSaleWithRelations.taxAmount),
        totalAmount: Number(updatedSaleWithRelations.totalAmount),
        amountPaid: Number(updatedSaleWithRelations.amountPaid),
        changeAmount: Number(updatedSaleWithRelations.changeAmount),
        saleItems: updatedSaleWithRelations.saleItems.map(item => ({
          ...item,
          unitPrice: Number(item.unitPrice),
          totalPrice: Number(item.totalPrice),
          discountValue: item.discountValue ? Number(item.discountValue) : null,
          discountAmount: Number(item.discountAmount),
        })),
      };
    });
  }

  /**
   * Update only sale-level fields without touching items
   */
  private async updateDraftSaleFields(saleId: string, updateSaleDto: UpdateSaleDto) {
    // Calculate change amount if amountPaid is being updated
    let changeAmount: number | undefined;
    if (updateSaleDto.amountPaid !== undefined) {
      const sale = await this.prisma.sale.findUnique({ where: { id: saleId } });
      if (sale) {
        changeAmount = updateSaleDto.amountPaid - Number(sale.totalAmount);
      }
    }

    const updatedSale = await this.prisma.sale.update({
      where: { id: saleId },
      data: {
        customerId: updateSaleDto.customerId,
        customerName: updateSaleDto.customerName,
        customerPhone: updateSaleDto.customerPhone,
        notes: updateSaleDto.notes,
        paymentMethod: updateSaleDto.paymentMethod,
        amountPaid: updateSaleDto.amountPaid,
        changeAmount: changeAmount,
        updatedAt: new Date(),
      },
    });

    // Convert Decimal fields to numbers for API response
    return {
      ...updatedSale,
      subtotal: Number(updatedSale.subtotal),
      discountValue: updatedSale.discountValue ? Number(updatedSale.discountValue) : null,
      discountAmount: Number(updatedSale.discountAmount),
      taxAmount: Number(updatedSale.taxAmount),
      totalAmount: Number(updatedSale.totalAmount),
      amountPaid: Number(updatedSale.amountPaid),
      changeAmount: Number(updatedSale.changeAmount),
    };
  }
}
