import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class SaleNumberGeneratorService {
  constructor(private prisma: PrismaService) { }

  async generateSaleNumber(): Promise<string> {
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');

    // Format: TRX-YYYYMMDD-XXX
    const datePrefix = `TRX-${year}${month}${day}`;

    // Use a more robust approach to find the next sequence
    // Get all sales for today and extract sequences
    const existingSales = await this.prisma.sale.findMany({
      where: {
        saleNumber: {
          startsWith: datePrefix,
        },
      },
      select: {
        saleNumber: true,
      },
    });

    // Extract all sequence numbers and find the maximum
    const sequences = existingSales
      .map(sale => {
        const match = sale.saleNumber.match(/-(\d{3})$/);
        return match ? parseInt(match[1], 10) : 0;
      })
      .filter(seq => seq > 0);

    const maxSequence = sequences.length > 0 ? Math.max(...sequences) : 0;
    const nextSequence = maxSequence + 1;

    // Add some randomness to avoid collisions in high-concurrency scenarios
    const timestamp = Date.now().toString().slice(-2);
    const finalSequence = nextSequence + parseInt(timestamp) % 10;

    return `${datePrefix}-${finalSequence.toString().padStart(3, '0')}`;
  }

  async validateSaleNumberUniqueness(saleNumber: string): Promise<boolean> {
    const existing = await this.prisma.sale.findUnique({
      where: { saleNumber },
    });
    return !existing;
  }
}
