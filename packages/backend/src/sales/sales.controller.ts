import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  BadRequestException,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import { SalesService } from './sales.service';
import { CreateSaleDto } from './dto/create-sale.dto';
import { UpdateSaleDto } from './dto/update-sale.dto';
import { SaleQueryDto } from './dto/sale-query.dto';
import { SaleNumberGeneratorService } from './sale-number-generator.service';
import { ReceiptService } from './receipt.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ManagerGuard } from '../suppliers/guards/manager.guard';

@Controller('sales')
@UseGuards(JwtAuthGuard)
export class SalesController {
  constructor(
    private readonly salesService: SalesService,
    private readonly saleNumberGeneratorService: SaleNumberGeneratorService,
    private readonly receiptService: ReceiptService,
  ) { }

  @Post()
  async create(@Body() createSaleDto: CreateSaleDto, @Request() req) {
    // Set cashier from authenticated user if not provided
    if (!createSaleDto.cashierId) {
      createSaleDto.cashierId = req.user.id;
    }
    return this.salesService.create(createSaleDto);
  }

  @Get()
  async findAll(@Query() query: SaleQueryDto) {
    return this.salesService.findAll(query);
  }

  @Get('stats')
  async getStats() {
    return this.salesService.getStats();
  }

  // Sale number generation endpoint
  @Get('generate-number')
  async generateSaleNumber() {
    return {
      saleNumber: await this.saleNumberGeneratorService.generateSaleNumber(),
    };
  }

  @Get('validate-number/:number')
  async validateSaleNumber(@Param('number') number: string) {
    return {
      isUnique: await this.saleNumberGeneratorService.validateSaleNumberUniqueness(number),
    };
  }

  // Enhanced Transaction Processing Endpoints (must come before :id routes)

  @Post('draft')
  async createDraft(@Body() createSaleDto: CreateSaleDto, @Request() req) {
    // Set cashier from authenticated user if not provided
    if (!createSaleDto.cashierId) {
      createSaleDto.cashierId = req.user.id;
    }
    return this.salesService.createDraft(createSaleDto);
  }

  // Receipt Generation Endpoints (must come before :id routes)

  @Get(':id/receipt/validate')
  async validateReceiptGeneration(@Param('id') id: string) {
    return this.receiptService.validateReceiptGeneration(id);
  }

  @Get(':id/receipt/wide')
  async generateReceiptWide(@Param('id') id: string) {
    return this.receiptService.generateReceiptWide(id);
  }

  @Get(':id/receipt/wide/pdf')
  async generateReceiptWidePdf(@Param('id') id: string, @Res() res: Response) {
    const pdfBuffer = await this.receiptService.generateReceiptPdf(id, true);
    const sale = await this.salesService.findOne(id);

    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="struk-lebar-${sale.saleNumber}.pdf"`,
      'Content-Length': pdfBuffer.length,
    });

    res.send(pdfBuffer);
  }

  @Get(':id/receipt')
  async generateReceipt(@Param('id') id: string) {
    return this.receiptService.generateReceipt(id);
  }

  @Get(':id/receipt/pdf')
  async generateReceiptPdf(@Param('id') id: string, @Res() res: Response) {
    const pdfBuffer = await this.receiptService.generateReceiptPdf(id, false);
    const sale = await this.salesService.findOne(id);

    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="struk-${sale.saleNumber}.pdf"`,
      'Content-Length': pdfBuffer.length,
    });

    res.send(pdfBuffer);
  }

  // Transaction status endpoints (must come before :id routes)

  @Patch(':id/complete')
  async completeSale(@Param('id') id: string, @Request() req) {
    return this.salesService.completeSale(id, req.user.id);
  }

  @Patch(':id/cancel')
  async cancelSale(@Param('id') id: string, @Body() body: { reason?: string }, @Request() req) {
    return this.salesService.cancelSale(id, req.user.id, body.reason);
  }

  @Patch(':id/refund')
  @UseGuards(ManagerGuard) // Only managers can process refunds
  async refundSale(@Param('id') id: string, @Body() body: { reason?: string }, @Request() req) {
    return this.salesService.refundSale(id, req.user.id, body.reason);
  }

  // Generic routes (must come last)

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.salesService.findOne(id);
  }

  @Patch(':id')
  async updateDraft(@Param('id') id: string, @Body() updateSaleDto: UpdateSaleDto) {
    return this.salesService.updateDraft(id, updateSaleDto);
  }
}
