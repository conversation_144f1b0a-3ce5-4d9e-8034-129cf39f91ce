import { PrismaClient, UserRole } from '@prisma/client';
import * as bcrypt from 'bcrypt';
import { seedAllSupplierData } from './seed-suppliers';
import { seedAllProductData } from './seed-products';
import { seedAllInventoryData } from './seed-inventory';
import { seedAllSalesCustomerData } from './seed-sales-customers';

const prisma = new PrismaClient();

async function seedApotekSettings() {
  console.log('🌱 Seeding Apotek Settings...');

  const settings = [
    {
      settingKey: 'pharmacyName',
      settingValue: 'Apotek Sehat Bersama',
    },
    {
      settingKey: 'pharmacyDescription',
      settingValue: 'Apotek terpercaya yang melayani kebutuhan kesehatan masyarakat dengan obat-obatan berkualitas dan pelayanan profesional.',
    },
    {
      settingKey: 'pharmacyAddress',
      settingValue: 'J<PERSON>. <PERSON><PERSON><PERSON><PERSON>. 123, <PERSON><PERSON><PERSON><PERSON>, Kecamatan Sejahtera, Jakarta Selatan 12345',
    },
    {
      settingKey: 'pharmacyPhone',
      settingValue: '+62 21 1234 5678',
    },
    {
      settingKey: 'pharmacyLicense',
      settingValue: 'SIA.503/31.74.02.1009.13.07/2024',
    },
    {
      settingKey: 'operatingHours',
      settingValue: 'Senin - Sabtu: 08:00 - 22:00 WIB, Minggu: 09:00 - 21:00 WIB',
    },
    {
      settingKey: 'emergencyContact',
      settingValue: '+62 812 3456 7890',
    },
  ];

  for (const setting of settings) {
    await prisma.appSettings.upsert({
      where: { settingKey: setting.settingKey },
      update: { settingValue: setting.settingValue },
      create: setting,
    });
  }

  console.log('✅ Apotek Settings seeded successfully!');
}

async function seedUser() {
  console.log('🌱 Seeding User...');

  const password = bcrypt.hashSync('password123', 12)

  const user = {
    email: '<EMAIL>',
    password: password,
    firstName: 'Admin',
    lastName: 'Apotek',
    role: UserRole.ADMIN,
  };

  await prisma.user.upsert({
    where: { email: user.email },
    update: user,
    create: user,
  });

  console.log('✅ User seeded successfully!');
}

async function main() {
  try {
    await seedApotekSettings();
    await seedUser();
    await seedAllSupplierData();
    await seedAllProductData();
    await seedAllInventoryData();
    await seedAllSalesCustomerData();
  } catch (error) {
    console.error('❌ Error seeding data:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}

export { seedApotekSettings };
