import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { Decimal } from '@prisma/client/runtime/library';

export interface UnitConversionResult {
  success: boolean;
  convertedQuantity?: number;
  error?: string;
}

export interface UnitHierarchyNode {
  id: string;
  unitId: string;
  unitName: string;
  unitAbbreviation: string;
  level: number;
  conversionFactor: Decimal;
  parentUnitId?: string;
  children: UnitHierarchyNode[];
}

@Injectable()
export class UnitConversionService {
  constructor(private prisma: PrismaService) {}

  /**
   * Convert quantity from one unit to another for a specific product
   * @param productId - The product ID
   * @param fromUnitId - Source unit ID
   * @param toUnitId - Target unit ID
   * @param quantity - Quantity to convert
   * @returns Conversion result with converted quantity or error
   */
  async convertUnits(
    productId: string,
    fromUnitId: string,
    toUnitId: string,
    quantity: number,
  ): Promise<UnitConversionResult> {
    try {
      // If converting to the same unit, return the same quantity
      if (fromUnitId === toUnitId) {
        return {
          success: true,
          convertedQuantity: quantity,
        };
      }

      // Get the unit hierarchy for the product
      const hierarchy = await this.getProductUnitHierarchy(productId);
      
      if (!hierarchy) {
        return {
          success: false,
          error: 'Product unit hierarchy not found',
        };
      }

      // Find the conversion path between units
      const conversionFactor = this.calculateConversionFactor(
        hierarchy,
        fromUnitId,
        toUnitId,
      );

      if (conversionFactor === null) {
        return {
          success: false,
          error: 'No conversion path found between units',
        };
      }

      const convertedQuantity = quantity * conversionFactor;

      return {
        success: true,
        convertedQuantity: Math.round(convertedQuantity * 10000) / 10000, // Round to 4 decimal places
      };
    } catch (error) {
      return {
        success: false,
        error: `Conversion failed: ${error.message}`,
      };
    }
  }

  /**
   * Convert any unit to base unit for a product
   * @param productId - The product ID
   * @param fromUnitId - Source unit ID
   * @param quantity - Quantity to convert
   * @returns Quantity in base units
   */
  async convertToBaseUnit(
    productId: string,
    fromUnitId: string,
    quantity: number,
  ): Promise<UnitConversionResult> {
    try {
      // Get product base unit
      const product = await this.prisma.product.findUnique({
        where: { id: productId },
        include: { baseUnit: true },
      });

      if (!product) {
        return {
          success: false,
          error: 'Product not found',
        };
      }

      return this.convertUnits(productId, fromUnitId, product.baseUnitId, quantity);
    } catch (error) {
      return {
        success: false,
        error: `Base unit conversion failed: ${error.message}`,
      };
    }
  }

  /**
   * Convert from base unit to any other unit for a product
   * @param productId - The product ID
   * @param toUnitId - Target unit ID
   * @param baseQuantity - Quantity in base units
   * @returns Quantity in target units
   */
  async convertFromBaseUnit(
    productId: string,
    toUnitId: string,
    baseQuantity: number,
  ): Promise<UnitConversionResult> {
    try {
      // Get product base unit
      const product = await this.prisma.product.findUnique({
        where: { id: productId },
        include: { baseUnit: true },
      });

      if (!product) {
        return {
          success: false,
          error: 'Product not found',
        };
      }

      return this.convertUnits(productId, product.baseUnitId, toUnitId, baseQuantity);
    } catch (error) {
      return {
        success: false,
        error: `From base unit conversion failed: ${error.message}`,
      };
    }
  }

  /**
   * Get the complete unit hierarchy for a product
   * @param productId - The product ID
   * @returns Unit hierarchy tree
   */
  async getProductUnitHierarchy(productId: string): Promise<UnitHierarchyNode[]> {
    const hierarchies = await this.prisma.productUnitHierarchy.findMany({
      where: {
        productId,
        isActive: true,
      },
      include: {
        unit: true,
      },
      orderBy: {
        level: 'asc',
      },
    });

    if (hierarchies.length === 0) {
      return [];
    }

    // Build the hierarchy tree
    const nodeMap = new Map<string, UnitHierarchyNode>();
    const rootNodes: UnitHierarchyNode[] = [];

    // Create all nodes
    hierarchies.forEach((hierarchy) => {
      const node: UnitHierarchyNode = {
        id: hierarchy.id,
        unitId: hierarchy.unitId,
        unitName: hierarchy.unit.name,
        unitAbbreviation: hierarchy.unit.abbreviation,
        level: hierarchy.level,
        conversionFactor: hierarchy.conversionFactor,
        parentUnitId: hierarchy.parentUnitId || undefined,
        children: [],
      };
      nodeMap.set(hierarchy.unitId, node);
    });

    // Build parent-child relationships
    hierarchies.forEach((hierarchy) => {
      const node = nodeMap.get(hierarchy.unitId);
      if (node) {
        if (hierarchy.parentUnitId) {
          const parent = nodeMap.get(hierarchy.parentUnitId);
          if (parent) {
            parent.children.push(node);
          }
        } else {
          rootNodes.push(node);
        }
      }
    });

    return rootNodes;
  }

  /**
   * Calculate conversion factor between two units in a hierarchy
   * @param hierarchy - The unit hierarchy tree
   * @param fromUnitId - Source unit ID
   * @param toUnitId - Target unit ID
   * @returns Conversion factor or null if no path exists
   */
  private calculateConversionFactor(
    hierarchy: UnitHierarchyNode[],
    fromUnitId: string,
    toUnitId: string,
  ): number | null {
    // Create a flat map of all nodes for easier lookup
    const nodeMap = new Map<string, UnitHierarchyNode>();

    const flattenHierarchy = (nodes: UnitHierarchyNode[]) => {
      nodes.forEach((node) => {
        nodeMap.set(node.unitId, node);
        flattenHierarchy(node.children);
      });
    };

    flattenHierarchy(hierarchy);

    const fromNode = nodeMap.get(fromUnitId);
    const toNode = nodeMap.get(toUnitId);

    if (!fromNode || !toNode) {
      return null;
    }

    // Same unit - no conversion needed
    if (fromNode.unitId === toNode.unitId) {
      return 1;
    }

    // Convert between different units using conversion factors
    const fromFactor = Number(fromNode.conversionFactor);
    const toFactor = Number(toNode.conversionFactor);

    // Calculate the conversion ratio
    // fromFactor represents how many base units are in one fromNode unit
    // toFactor represents how many base units are in one toNode unit
    // So the conversion is: fromFactor / toFactor

    return fromFactor / toFactor;
  }

  /**
   * Get conversion factor from a unit to base unit
   */
  private getConversionToBase(
    node: UnitHierarchyNode,
    nodeMap: Map<string, UnitHierarchyNode>,
  ): number | null {
    if (node.level === 0) {
      return 1; // Already base unit
    }

    let factor = 1;
    let currentNode = node;

    while (currentNode.level > 0) {
      factor *= 1 / Number(currentNode.conversionFactor);
      
      if (!currentNode.parentUnitId) {
        return null;
      }
      
      const parentNode = nodeMap.get(currentNode.parentUnitId);
      if (!parentNode) {
        return null;
      }
      
      currentNode = parentNode;
    }

    return factor;
  }

  /**
   * Get conversion factor from base unit to a specific unit
   */
  private getConversionFromBase(
    node: UnitHierarchyNode,
    nodeMap: Map<string, UnitHierarchyNode>,
  ): number | null {
    if (node.level === 0) {
      return 1; // Already base unit
    }

    let factor = 1;
    const path: UnitHierarchyNode[] = [];
    let currentNode = node;

    // Build path from target to base
    while (currentNode.level > 0) {
      path.unshift(currentNode);
      
      if (!currentNode.parentUnitId) {
        return null;
      }
      
      const parentNode = nodeMap.get(currentNode.parentUnitId);
      if (!parentNode) {
        return null;
      }
      
      currentNode = parentNode;
    }

    // Calculate factor from base to target
    path.forEach((pathNode) => {
      factor *= Number(pathNode.conversionFactor);
    });

    return factor;
  }

  /**
   * Validate unit hierarchy for a product
   * @param productId - The product ID
   * @returns Validation result
   */
  async validateUnitHierarchy(productId: string): Promise<{
    isValid: boolean;
    errors: string[];
  }> {
    const errors: string[] = [];
    
    try {
      const hierarchies = await this.prisma.productUnitHierarchy.findMany({
        where: {
          productId,
          isActive: true,
        },
        include: {
          unit: true,
        },
      });

      if (hierarchies.length === 0) {
        errors.push('No unit hierarchy found for product');
        return { isValid: false, errors };
      }

      // Check for base unit (level 0)
      const baseUnits = hierarchies.filter(h => h.level === 0);
      if (baseUnits.length === 0) {
        errors.push('No base unit (level 0) found');
      } else if (baseUnits.length > 1) {
        errors.push('Multiple base units found');
      }

      // Check for circular references
      const visited = new Set<string>();
      const recursionStack = new Set<string>();
      
      const hasCircularReference = (unitId: string): boolean => {
        if (recursionStack.has(unitId)) {
          return true;
        }
        if (visited.has(unitId)) {
          return false;
        }

        visited.add(unitId);
        recursionStack.add(unitId);

        const hierarchy = hierarchies.find(h => h.unitId === unitId);
        if (hierarchy?.parentUnitId) {
          if (hasCircularReference(hierarchy.parentUnitId)) {
            return true;
          }
        }

        recursionStack.delete(unitId);
        return false;
      };

      hierarchies.forEach(h => {
        if (hasCircularReference(h.unitId)) {
          errors.push(`Circular reference detected in unit hierarchy for unit ${h.unit.name}`);
        }
      });

      return {
        isValid: errors.length === 0,
        errors,
      };
    } catch (error) {
      errors.push(`Validation failed: ${error.message}`);
      return { isValid: false, errors };
    }
  }
}
