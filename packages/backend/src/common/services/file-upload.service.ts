import { Injectable } from '@nestjs/common';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { existsSync, mkdirSync, unlinkSync } from 'fs';

@Injectable()
export class FileUploadService {
  private readonly uploadPath = './uploads';

  constructor() {
    // Ensure upload directory exists
    if (!existsSync(this.uploadPath)) {
      mkdirSync(this.uploadPath, { recursive: true });
    }
  }

  getMulterOptions() {
    return {
      storage: diskStorage({
        destination: (req, file, cb) => {
          const uploadPath = './uploads/supplier-documents';
          if (!existsSync(uploadPath)) {
            mkdirSync(uploadPath, { recursive: true });
          }
          cb(null, uploadPath);
        },
        filename: (req, file, cb) => {
          const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
          const ext = extname(file.originalname);
          cb(null, `${file.fieldname}-${uniqueSuffix}${ext}`);
        },
      }),
      fileFilter: (req, file, cb) => {
        // Allow common document types
        const allowedMimes = [
          'application/pdf',
          'image/jpeg',
          'image/png',
          'image/jpg',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ];
        
        if (allowedMimes.includes(file.mimetype)) {
          cb(null, true);
        } else {
          cb(new Error('File type not allowed'), false);
        }
      },
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
      },
    };
  }

  deleteFile(filePath: string): boolean {
    if (!filePath) {
      console.warn('FileUploadService.deleteFile: No file path provided');
      return false;
    }

    try {
      if (existsSync(filePath)) {
        unlinkSync(filePath);
        console.log(`FileUploadService: Successfully deleted file: ${filePath}`);
        return true;
      } else {
        console.warn(`FileUploadService: File not found: ${filePath}`);
        return false;
      }
    } catch (error) {
      console.error(`FileUploadService: Error deleting file ${filePath}:`, error);
      throw error;
    }
  }

  getSupplierDocumentFileUrl(filename: string): string {
    return `/uploads/supplier-documents/${filename}`;
  }

  getPaymentProofFileUrl(filename: string): string {
    return `/uploads/payment-proofs/${filename}`;
  }
}
