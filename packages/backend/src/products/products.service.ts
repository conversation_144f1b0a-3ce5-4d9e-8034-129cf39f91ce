import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ProductQueryDto } from './dto/product-query.dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class ProductsService {
  constructor(private readonly prisma: PrismaService) {}

  async create(createProductDto: CreateProductDto, userId: string) {
    // Validate required fields
    if (!createProductDto.code) {
      throw new BadRequestException('Kode produk wajib diisi');
    }

    // Check if product code already exists
    const existingProduct = await this.prisma.product.findUnique({
      where: { code: createProductDto.code },
    });

    if (existingProduct) {
      throw new ConflictException('Kode produk sudah digunakan');
    }

    // Validate base unit exists
    const baseUnit = await this.prisma.productUnit.findUnique({
      where: { id: createProductDto.baseUnitId },
    });

    if (!baseUnit) {
      throw new BadRequestException('Unit dasar tidak ditemukan');
    }

    const { unitHierarchies, ...productData } = createProductDto;

    // Validate unit hierarchies if provided
    if (unitHierarchies && unitHierarchies.length > 0) {
      await this.validateUnitHierarchies(unitHierarchies, createProductDto.baseUnitId);
    }

    try {
      const product = await this.prisma.product.create({
        data: {
          ...productData,
          createdBy: userId,
          updatedBy: userId,
          unitHierarchies: unitHierarchies ? {
            create: unitHierarchies.map(hierarchy => ({
              ...hierarchy,
              sellingPrice: hierarchy.sellingPrice ? new Prisma.Decimal(hierarchy.sellingPrice) : null,
              costPrice: hierarchy.costPrice ? new Prisma.Decimal(hierarchy.costPrice) : null,
              conversionFactor: new Prisma.Decimal(hierarchy.conversionFactor),
            }))
          } : undefined,
        },
        include: {
          baseUnit: true,
          unitHierarchies: {
            include: {
              unit: true,
              parentUnit: {
                include: {
                  unit: true,
                },
              },
            },
          },
          createdByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      return product;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Kode produk sudah digunakan');
        }
      }
      throw error;
    }
  }

  async findAll(query: ProductQueryDto) {
    const {
      page = 1,
      limit = 10,
      search,
      type,
      category,
      medicineClassification,
      manufacturer,
      isActive,
      baseUnitId,
      genericName,
      bpomNumber,
      activeIngredient,
      dosageForm,
      strength,
      createdBy,
      dateFrom,
      dateTo,
      lowStock,
      outOfStock,
      overStock,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = query;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: Prisma.ProductWhereInput = {};

    // Search functionality
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { code: { contains: search, mode: 'insensitive' } },
        { genericName: { contains: search, mode: 'insensitive' } },
        { bpomNumber: { contains: search, mode: 'insensitive' } },
        { manufacturer: { contains: search, mode: 'insensitive' } },
        { activeIngredient: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Filters
    if (type) where.type = type;
    if (category) where.category = category;
    if (medicineClassification) where.medicineClassification = medicineClassification;
    if (manufacturer) where.manufacturer = { contains: manufacturer, mode: 'insensitive' };
    if (isActive !== undefined) where.isActive = isActive;
    if (baseUnitId) where.baseUnitId = baseUnitId;
    if (genericName) where.genericName = { contains: genericName, mode: 'insensitive' };
    if (bpomNumber) where.bpomNumber = { contains: bpomNumber, mode: 'insensitive' };
    if (activeIngredient) where.activeIngredient = { contains: activeIngredient, mode: 'insensitive' };
    if (dosageForm) where.dosageForm = { contains: dosageForm, mode: 'insensitive' };
    if (strength) where.strength = { contains: strength, mode: 'insensitive' };
    if (createdBy) where.createdBy = createdBy;

    // Date range filters
    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) where.createdAt.gte = new Date(dateFrom);
      if (dateTo) where.createdAt.lte = new Date(dateTo);
    }

    // Stock level filters (these would require inventory data - placeholder for now)
    // TODO: Implement stock level filtering when inventory system is ready
    if (lowStock || outOfStock || overStock) {
      // This will be implemented when inventory management is added
    }

    // Sorting
    const getOrderByField = (field: string): Prisma.ProductOrderByWithRelationInput => {
      const orderDirection = sortOrder === 'asc' ? 'asc' : 'desc';

      switch (field) {
        case 'name':
          return { name: orderDirection };
        case 'code':
          return { code: orderDirection };
        case 'type':
          return { type: orderDirection };
        case 'category':
          return { category: orderDirection };
        case 'manufacturer':
          return { manufacturer: orderDirection };
        case 'medicineClassification':
          return { medicineClassification: orderDirection };
        case 'createdAt':
          return { createdAt: orderDirection };
        case 'updatedAt':
          return { updatedAt: orderDirection };
        default:
          return { createdAt: orderDirection };
      }
    };

    const orderBy = getOrderByField(sortBy);

    const [products, total] = await Promise.all([
      this.prisma.product.findMany({
        where,
        skip,
        take: limit,
        orderBy,
        include: {
          baseUnit: true,
          unitHierarchies: {
            include: {
              unit: true,
            },
            orderBy: { level: 'asc' },
          },
          createdByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      }),
      this.prisma.product.count({ where }),
    ]);

    return {
      data: products,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPreviousPage: page > 1,
      },
    };
  }

  async findOne(id: string) {
    const product = await this.prisma.product.findUnique({
      where: { id },
      include: {
        baseUnit: true,
        unitHierarchies: {
          include: {
            unit: true,
            parentUnit: {
              include: {
                unit: true,
              },
            },
            childUnits: {
              include: {
                unit: true,
              },
            },
          },
          orderBy: { level: 'asc' },
        },
        createdByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        updatedByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    if (!product) {
      throw new NotFoundException('Produk tidak ditemukan');
    }

    return product;
  }

  async update(id: string, updateProductDto: UpdateProductDto, userId: string) {
    const product = await this.findOne(id);

    // Check if code is being changed and if it conflicts
    if (updateProductDto.code && updateProductDto.code !== product.code) {
      const existingProduct = await this.prisma.product.findUnique({
        where: { code: updateProductDto.code },
      });

      if (existingProduct) {
        throw new ConflictException('Kode produk sudah digunakan');
      }
    }

    // Validate base unit if being changed
    if (updateProductDto.baseUnitId && updateProductDto.baseUnitId !== product.baseUnitId) {
      const baseUnit = await this.prisma.productUnit.findUnique({
        where: { id: updateProductDto.baseUnitId },
      });

      if (!baseUnit) {
        throw new BadRequestException('Unit dasar tidak ditemukan');
      }
    }

    const { unitHierarchies, ...productData } = updateProductDto;

    // Validate unit hierarchies if provided
    if (unitHierarchies && unitHierarchies.length > 0) {
      const baseUnitId = updateProductDto.baseUnitId || product.baseUnitId;
      await this.validateUnitHierarchies(unitHierarchies, baseUnitId);
    }

    try {
      const updatedProduct = await this.prisma.$transaction(async (prisma) => {
        // Update product data
        await prisma.product.update({
          where: { id },
          data: {
            ...productData,
            updatedBy: userId,
            updatedAt: new Date(),
          },
        });

        // Handle unit hierarchies if provided
        if (unitHierarchies) {
          // Process each hierarchy
          for (const hierarchy of unitHierarchies) {
            const hierarchyData = {
              ...hierarchy,
              sellingPrice: hierarchy.sellingPrice ? new Prisma.Decimal(hierarchy.sellingPrice) : null,
              costPrice: hierarchy.costPrice ? new Prisma.Decimal(hierarchy.costPrice) : null,
              conversionFactor: new Prisma.Decimal(hierarchy.conversionFactor),
            };

            if (hierarchy.id) {
              // Update existing hierarchy
              await prisma.productUnitHierarchy.update({
                where: { id: hierarchy.id },
                data: hierarchyData,
              });
            } else {
              // Create new hierarchy
              await prisma.productUnitHierarchy.create({
                data: {
                  ...hierarchyData,
                  productId: id,
                },
              });
            }
          }

          // Mark hierarchies as inactive if they're not in the update list
          const updatedHierarchyIds = unitHierarchies
            .filter(h => h.id)
            .map(h => h.id)
            .filter((id): id is string => id !== undefined);

          if (updatedHierarchyIds.length > 0) {
            await prisma.productUnitHierarchy.updateMany({
              where: {
                productId: id,
                id: { notIn: updatedHierarchyIds },
              },
              data: { isActive: false },
            });
          }
        }

        // Return updated product with relations
        return prisma.product.findUnique({
          where: { id },
          include: {
            baseUnit: true,
            unitHierarchies: {
              include: {
                unit: true,
                parentUnit: {
                  include: {
                    unit: true,
                  },
                },
              },
              orderBy: { level: 'asc' },
            },
            createdByUser: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            updatedByUser: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        });
      });

      return updatedProduct;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Kode produk sudah digunakan');
        }
      }
      throw error;
    }
  }

  async deactivate(id: string, userId: string) {
    // Ensure product exists
    const product = await this.findOne(id);

    if (!product.isActive) {
      throw new ConflictException('Produk sudah dalam status tidak aktif');
    }

    // Soft delete by setting isActive to false
    return this.prisma.product.update({
      where: { id },
      data: {
        isActive: false,
        updatedBy: userId,
        updatedAt: new Date(),
      },
      include: {
        baseUnit: true,
        unitHierarchies: {
          include: {
            unit: true,
          },
          orderBy: { level: 'asc' },
        },
        createdByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        updatedByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });
  }

  async activate(id: string, userId: string) {
    // Ensure product exists
    const product = await this.findOne(id);

    if (product.isActive) {
      throw new ConflictException('Produk sudah dalam status aktif');
    }

    // Activate by setting isActive to true
    return this.prisma.product.update({
      where: { id },
      data: {
        isActive: true,
        updatedBy: userId,
        updatedAt: new Date(),
      },
      include: {
        baseUnit: true,
        unitHierarchies: {
          include: {
            unit: true,
          },
          orderBy: { level: 'asc' },
        },
        createdByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        updatedByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });
  }

  async hardDelete(id: string) {
    // Ensure product exists
    await this.findOne(id);

    // Check if product has any dependencies (inventory, sales, etc.)
    // TODO: Add checks for inventory items, sales, etc. when those modules are implemented

    // Hard delete the product (cascading will handle unit hierarchies)
    await this.prisma.product.delete({
      where: { id },
    });
  }

  async getStats() {
    const [
      total,
      active,
      inactive,
      byType,
      byCategory,
      byMedicineClassification,
    ] = await Promise.all([
      this.prisma.product.count(),
      this.prisma.product.count({ where: { isActive: true } }),
      this.prisma.product.count({ where: { isActive: false } }),
      this.prisma.product.groupBy({
        by: ['type'],
        _count: { type: true },
      }),
      this.prisma.product.groupBy({
        by: ['category'],
        _count: { category: true },
      }),
      this.prisma.product.groupBy({
        by: ['medicineClassification'],
        _count: { medicineClassification: true },
      }),
    ]);

    return {
      total,
      active,
      inactive,
      byType: byType.reduce((acc, item) => {
        acc[item.type] = item._count.type;
        return acc;
      }, {}),
      byCategory: byCategory.reduce((acc, item) => {
        acc[item.category] = item._count.category;
        return acc;
      }, {}),
      byMedicineClassification: byMedicineClassification.reduce((acc, item) => {
        acc[item.medicineClassification] = item._count.medicineClassification;
        return acc;
      }, {}),
    };
  }

  /**
   * Validate unit hierarchies to prevent circular references and ensure proper level ordering
   */
  private async validateUnitHierarchies(unitHierarchies: any[], baseUnitId: string) {
    // Check if all units exist
    const unitIds = unitHierarchies.map(h => h.unitId);
    const existingUnits = await this.prisma.productUnit.findMany({
      where: { id: { in: unitIds } },
    });

    if (existingUnits.length !== unitIds.length) {
      const missingIds = unitIds.filter(id => !existingUnits.find(u => u.id === id));
      throw new BadRequestException(`Unit tidak ditemukan: ${missingIds.join(', ')}`);
    }

    // Validate that base unit is included in hierarchy
    const baseUnitInHierarchy = unitHierarchies.find(h => h.unitId === baseUnitId);
    if (!baseUnitInHierarchy) {
      throw new BadRequestException('Unit dasar harus disertakan dalam hierarki unit');
    }

    // Validate that base unit has level 0
    if (baseUnitInHierarchy.level !== 0) {
      throw new BadRequestException('Unit dasar harus memiliki level 0');
    }

    // Validate that base unit has conversion factor 1
    if (baseUnitInHierarchy.conversionFactor !== 1) {
      throw new BadRequestException('Unit dasar harus memiliki faktor konversi 1');
    }

    // Validate level ordering and prevent gaps
    const sortedHierarchies = [...unitHierarchies].sort((a, b) => a.level - b.level);
    for (let i = 0; i < sortedHierarchies.length; i++) {
      const hierarchy = sortedHierarchies[i];

      // Check for proper level sequence (0, 1, 2, 3, ...)
      if (hierarchy.level !== i) {
        throw new BadRequestException(`Level hierarki harus berurutan mulai dari 0. Level ${i} tidak ditemukan.`);
      }

      // Validate conversion factors
      if (hierarchy.conversionFactor <= 0) {
        throw new BadRequestException('Faktor konversi harus lebih besar dari 0');
      }

      // Validate parent-child relationships
      if (hierarchy.level > 0) {
        const parentLevel = hierarchy.level - 1;
        const parentHierarchy = sortedHierarchies.find(h => h.level === parentLevel);

        if (!parentHierarchy) {
          throw new BadRequestException(`Parent unit untuk level ${hierarchy.level} tidak ditemukan`);
        }

        // Set parent unit ID for non-base units
        hierarchy.parentUnitId = parentHierarchy.unitId;
      } else {
        // Base unit should not have parent
        hierarchy.parentUnitId = null;
      }
    }

    // Check for circular references by building the hierarchy tree
    const hierarchyMap = new Map();
    for (const hierarchy of unitHierarchies) {
      hierarchyMap.set(hierarchy.unitId, hierarchy);
    }

    // Validate no unit appears twice
    const unitIdSet = new Set();
    for (const hierarchy of unitHierarchies) {
      if (unitIdSet.has(hierarchy.unitId)) {
        throw new BadRequestException(`Unit ${hierarchy.unitId} muncul lebih dari sekali dalam hierarki`);
      }
      unitIdSet.add(hierarchy.unitId);
    }

    // Validate conversion factor relationships make sense
    for (let i = 1; i < sortedHierarchies.length; i++) {
      const current = sortedHierarchies[i];
      const parent = sortedHierarchies[i - 1];

      if (current.conversionFactor <= parent.conversionFactor) {
        throw new BadRequestException(
          `Faktor konversi untuk level ${current.level} (${current.conversionFactor}) harus lebih besar dari level ${parent.level} (${parent.conversionFactor})`
        );
      }
    }
  }
}
