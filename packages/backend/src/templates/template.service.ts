import { Injectable } from '@nestjs/common';
import { readFileSync } from 'fs';
import { join } from 'path';
import { ReceiptData } from '../sales/receipt.service';

@Injectable()
export class TemplateService {
  private receiptTemplate: string;

  constructor() {
    // Load the receipt template on service initialization
    this.loadReceiptTemplate();
  }

  /**
   * Load the receipt HTML template from file
   */
  private loadReceiptTemplate(): void {
    try {
      // Try multiple possible paths for the template
      const possiblePaths = [
        join(__dirname, '..', 'templates', 'receipt-template.html'),
        join(__dirname, '..', '..', 'src', 'templates', 'receipt-template.html'),
        join(process.cwd(), 'src', 'templates', 'receipt-template.html'),
        join(process.cwd(), 'packages', 'backend', 'src', 'templates', 'receipt-template.html')
      ];

      let templateLoaded = false;
      for (const templatePath of possiblePaths) {
        try {
          this.receiptTemplate = readFileSync(templatePath, 'utf-8');
          console.log(`Receipt template loaded from: ${templatePath}`);
          templateLoaded = true;
          break;
        } catch (err) {
          // Continue to next path
        }
      }

      if (!templateLoaded) {
        throw new Error('Template file not found in any expected location');
      }
    } catch (error) {
      console.error('Failed to load receipt template:', error);
      // Fallback to a basic template if file loading fails
      this.receiptTemplate = this.getBasicReceiptTemplate();
    }
  }

  /**
   * Generate HTML from receipt template with data
   */
  generateReceiptHtml(receiptData: ReceiptData): string {
    let html = this.receiptTemplate;

    // Replace template variables with actual data
    html = this.replaceTemplateVariables(html, receiptData);

    return html;
  }

  /**
   * Replace template placeholders with actual data
   */
  private replaceTemplateVariables(template: string, data: ReceiptData): string {
    let html = template;

    // Replace simple variables
    html = html.replace(/\{\{pharmacy\.name\}\}/g, this.escapeHtml(data.pharmacy.name));
    html = html.replace(/\{\{pharmacy\.address\}\}/g, this.escapeHtml(data.pharmacy.address));
    html = html.replace(/\{\{pharmacy\.phone\}\}/g, this.escapeHtml(data.pharmacy.phone));
    html = html.replace(/\{\{pharmacy\.email\}\}/g, data.pharmacy.email ? this.escapeHtml(data.pharmacy.email) : '');
    html = html.replace(/\{\{pharmacy\.license\}\}/g, data.pharmacy.license ? this.escapeHtml(data.pharmacy.license) : '');

    // Transaction data
    html = html.replace(/\{\{transaction\.saleNumber\}\}/g, this.escapeHtml(data.transaction.saleNumber));
    html = html.replace(/\{\{transaction\.date\}\}/g, this.escapeHtml(data.transaction.date));
    html = html.replace(/\{\{transaction\.time\}\}/g, this.escapeHtml(data.transaction.time));
    html = html.replace(/\{\{transaction\.cashier\.name\}\}/g, this.escapeHtml(data.transaction.cashier.name));

    // Customer data
    html = html.replace(/\{\{customer\.name\}\}/g, this.escapeHtml(data.customer.name));
    html = html.replace(/\{\{customer\.phone\}\}/g, data.customer.phone ? this.escapeHtml(data.customer.phone) : '');
    html = html.replace(/\{\{customer\.membershipNumber\}\}/g, data.customer.membershipNumber ? this.escapeHtml(data.customer.membershipNumber) : '');

    // Totals
    html = html.replace(/\{\{formatCurrency totals\.subtotal\}\}/g, this.formatCurrency(data.totals.subtotal));
    html = html.replace(/\{\{formatCurrency totals\.discount\}\}/g, this.formatCurrency(data.totals.discount));
    html = html.replace(/\{\{formatCurrency totals\.tax\}\}/g, this.formatCurrency(data.totals.tax));
    html = html.replace(/\{\{formatCurrency totals\.total\}\}/g, this.formatCurrency(data.totals.total));

    // Payment data
    html = html.replace(/\{\{payment\.method\}\}/g, this.escapeHtml(data.payment.method));
    html = html.replace(/\{\{formatCurrency payment\.amountPaid\}\}/g, this.formatCurrency(data.payment.amountPaid));
    html = html.replace(/\{\{formatCurrency payment\.change\}\}/g, this.formatCurrency(data.payment.change));

    // Footer data
    html = html.replace(/\{\{footer\.thankYouMessage\}\}/g, this.escapeHtml(data.footer.thankYouMessage));
    html = html.replace(/\{\{footer\.returnPolicy\}\}/g, this.escapeHtml(data.footer.returnPolicy));
    html = html.replace(/\{\{footer\.notes\}\}/g, data.footer.notes ? this.escapeHtml(data.footer.notes) : '');

    // Formatting variables
    html = html.replace(/\{\{paperWidth\}\}/g, data.formatting.paperWidth.toString());
    html = html.replace(/\{\{headerFontSize\}\}/g, this.getFontSize(data.formatting.fontSizes.header));
    html = html.replace(/\{\{totalsFontSize\}\}/g, this.getFontSize(data.formatting.fontSizes.totals));

    // Current date/time
    html = html.replace(/\{\{currentDateTime\}\}/g, new Date().toLocaleString('id-ID'));

    // Handle conditional blocks and loops
    html = this.processConditionals(html, data);
    html = this.processItemsLoop(html, data.items);

    return html;
  }

  /**
   * Process conditional blocks ({{#if}} and {{/if}})
   */
  private processConditionals(html: string, data: ReceiptData): string {
    // Process pharmacy email conditional
    html = html.replace(/\{\{#if pharmacy\.email\}\}(.*?)\{\{\/if\}\}/gs,
      data.pharmacy.email ? '$1' : '');

    // Process pharmacy license conditional
    html = html.replace(/\{\{#if pharmacy\.license\}\}(.*?)\{\{\/if\}\}/gs,
      data.pharmacy.license ? '$1' : '');

    // Process customer phone conditional
    html = html.replace(/\{\{#if customer\.phone\}\}(.*?)\{\{\/if\}\}/gs,
      data.customer.phone ? '$1' : '');

    // Process customer membership number conditional
    html = html.replace(/\{\{#if customer\.membershipNumber\}\}(.*?)\{\{\/if\}\}/gs,
      data.customer.membershipNumber ? '$1' : '');

    // Process totals conditionals
    html = html.replace(/\{\{#if totals\.discount\}\}(.*?)\{\{\/if\}\}/gs,
      data.totals.discount > 0 ? '$1' : '');

    html = html.replace(/\{\{#if totals\.tax\}\}(.*?)\{\{\/if\}\}/gs,
      data.totals.tax > 0 ? '$1' : '');

    // Process payment change conditional
    html = html.replace(/\{\{#if payment\.change\}\}(.*?)\{\{\/if\}\}/gs,
      data.payment.change > 0 ? '$1' : '');

    // Process footer notes conditional
    html = html.replace(/\{\{#if footer\.notes\}\}(.*?)\{\{\/if\}\}/gs,
      data.footer.notes ? '$1' : '');

    return html;
  }

  /**
   * Process items loop ({{#each items}})
   */
  private processItemsLoop(html: string, items: ReceiptData['items']): string {
    const itemsRegex = /\{\{#each items\}\}(.*?)\{\{\/each\}\}/gs;
    const match = itemsRegex.exec(html);

    if (!match) return html;

    const itemTemplate = match[1];
    let itemsHtml = '';

    items.forEach(item => {
      let itemHtml = itemTemplate;

      // Replace item variables
      itemHtml = itemHtml.replace(/\{\{name\}\}/g, this.escapeHtml(item.name));
      itemHtml = itemHtml.replace(/\{\{code\}\}/g, this.escapeHtml(item.code));
      itemHtml = itemHtml.replace(/\{\{quantity\}\}/g, item.quantity.toString());
      itemHtml = itemHtml.replace(/\{\{unit\}\}/g, this.escapeHtml(item.unit));
      itemHtml = itemHtml.replace(/\{\{formatCurrency unitPrice\}\}/g, this.formatCurrency(item.unitPrice));
      itemHtml = itemHtml.replace(/\{\{formatCurrency subtotal\}\}/g, this.formatCurrency(item.subtotal));
      itemHtml = itemHtml.replace(/\{\{formatCurrency discount\}\}/g, this.formatCurrency(item.discount));
      itemHtml = itemHtml.replace(/\{\{manufacturer\}\}/g, item.manufacturer ? this.escapeHtml(item.manufacturer) : '');

      // Handle item conditionals
      itemHtml = itemHtml.replace(/\{\{#if manufacturer\}\}(.*?)\{\{\/if\}\}/gs,
        item.manufacturer ? '$1' : '');
      itemHtml = itemHtml.replace(/\{\{#if discount\}\}(.*?)\{\{\/if\}\}/gs,
        item.discount > 0 ? '$1' : '');

      itemsHtml += itemHtml;
    });

    return html.replace(itemsRegex, itemsHtml);
  }

  /**
   * Format currency for Indonesian Rupiah
   */
  private formatCurrency(amount: number): string {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }

  /**
   * Get CSS font size from formatting enum
   */
  private getFontSize(size: 'normal' | 'large' | 'small'): string {
    switch (size) {
      case 'large': return '16px';
      case 'small': return '10px';
      default: return '12px';
    }
  }

  /**
   * Escape HTML to prevent XSS
   */
  private escapeHtml(text: string): string {
    const div = { innerHTML: '' } as any;
    div.textContent = text;
    return div.innerHTML || text.replace(/[&<>"']/g, (match: string) => {
      const escapeMap: { [key: string]: string } = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#39;'
      };
      return escapeMap[match];
    });
  }

  /**
   * Fallback basic template if file loading fails
   */
  private getBasicReceiptTemplate(): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Receipt</title>
    <style>
        body { font-family: 'Courier New', monospace; font-size: 12px; margin: 0; padding: 10px; }
        .center { text-align: center; }
        .bold { font-weight: bold; }
    </style>
</head>
<body>
    <div class="center bold">{{pharmacy.name}}</div>
    <div class="center">{{pharmacy.address}}</div>
    <div class="center">{{pharmacy.phone}}</div>
    <hr>
    <div>No: {{transaction.saleNumber}}</div>
    <div>Tanggal: {{transaction.date}} {{transaction.time}}</div>
    <div>Kasir: {{transaction.cashier.name}}</div>
    <div>Pelanggan: {{customer.name}}</div>
    <hr>
    {{#each items}}
    <div>{{name}} - {{quantity}} {{unit}} x {{formatCurrency unitPrice}} = {{formatCurrency subtotal}}</div>
    {{/each}}
    <hr>
    <div class="bold">Total: {{formatCurrency totals.total}}</div>
    <div>Bayar: {{formatCurrency payment.amountPaid}}</div>
    <div>Kembalian: {{formatCurrency payment.change}}</div>
    <hr>
    <div class="center">{{footer.thankYouMessage}}</div>
</body>
</html>`;
  }
}
