import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { 
  StockAllocationDto, 
  AllocationMethod, 
  AllocationResult, 
  AllocationBatch, 
  AllocationOptions 
} from '../dto/stock-allocation.dto';
import { StockMovementType } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

@Injectable()
export class StockAllocationService {
  constructor(private prisma: PrismaService) {}

  async allocateStock(
    allocationDto: StockAllocationDto,
    userId: string,
    options?: AllocationOptions
  ): Promise<AllocationResult> {
    const {
      productId,
      requestedQuantity,
      method,
      previewOnly = false,
      allowPartialAllocation = true,
      nearExpiryWarningDays = 30,
      reason,
      notes
    } = allocationDto;

    // Validate product exists
    const product = await this.prisma.product.findUnique({
      where: { id: productId },
      include: { baseUnit: true }
    });

    if (!product) {
      throw new NotFoundException(`Produk dengan ID ${productId} tidak ditemukan`);
    }

    if (!product.isActive) {
      throw new BadRequestException(`Produk ${product.name} tidak aktif`);
    }

    // Get available inventory items for allocation
    const availableItems = await this.getAvailableInventoryItems(productId, method);

    if (availableItems.length === 0) {
      return this.createEmptyAllocationResult(productId, requestedQuantity, method, previewOnly, [
        'Tidak ada item inventori yang tersedia untuk produk ini'
      ]);
    }

    // Perform allocation logic
    const allocationResult = await this.performAllocation(
      availableItems,
      requestedQuantity,
      method,
      nearExpiryWarningDays,
      allowPartialAllocation
    );

    // If not preview only, execute the allocation
    if (!previewOnly && allocationResult.allocatedQuantity > 0) {
      await this.executeAllocation(
        allocationResult.batches,
        productId,
        userId,
        reason || `Alokasi stok menggunakan metode ${method}`,
        notes
      );
    }

    return {
      ...allocationResult,
      productId,
      requestedQuantity,
      method,
      previewOnly
    };
  }

  private async getAvailableInventoryItems(productId: string, method: AllocationMethod) {
    const now = new Date();

    // Determine sort order based on method
    const orderBy = method === AllocationMethod.FEFO
      ? [
          { expiryDate: 'asc' as const },
          { receivedDate: 'asc' as const }, // Fallback to FIFO for same expiry dates
          { createdAt: 'asc' as const } // Final tie-breaker
        ]
      : [
          { receivedDate: 'asc' as const }, // FIFO: earliest received first
          { createdAt: 'asc' as const } // Tie-breaker for same received date
        ];

    // Use raw SQL to filter items with available quantity (quantityOnHand - quantityAllocated > 0)
    const rawQuery = `
      SELECT ii.*
      FROM "inventory_items" ii
      WHERE ii."productId" = $1
        AND ii."isActive" = true
        AND (ii."quantityOnHand" - ii."quantityAllocated") > 0
        AND (ii."expiryDate" IS NULL OR ii."expiryDate" > $2)
      ORDER BY ${method === AllocationMethod.FEFO
        ? 'ii."expiryDate" ASC NULLS LAST, ii."receivedDate" ASC, ii."createdAt" ASC'
        : 'ii."receivedDate" ASC, ii."createdAt" ASC'
      }
    `;

    const rawResults = await this.prisma.$queryRawUnsafe(rawQuery, productId, now) as any[];

    // Get full inventory items with relations for the filtered IDs
    if (rawResults.length === 0) {
      return [];
    }

    const itemIds = rawResults.map(item => item.id);

    return await this.prisma.inventoryItem.findMany({
      where: { id: { in: itemIds } },
      orderBy,
      include: {
        product: { include: { baseUnit: true } },
        unit: true,
        supplier: true
      }
    });
  }

  private async performAllocation(
    availableItems: any[],
    requestedQuantity: number,
    method: AllocationMethod,
    nearExpiryWarningDays: number,
    allowPartialAllocation: boolean
  ): Promise<Omit<AllocationResult, 'productId' | 'requestedQuantity' | 'method' | 'previewOnly'>> {
    const batches: AllocationBatch[] = [];
    const warnings: string[] = [];
    const errors: string[] = [];
    let remainingQuantity = requestedQuantity;
    let totalCost = 0;
    let totalAllocatedQuantity = 0;

    const now = new Date();
    const nearExpiryThreshold = new Date();
    nearExpiryThreshold.setDate(now.getDate() + nearExpiryWarningDays);

    for (const item of availableItems) {
      if (remainingQuantity <= 0) break;

      // NEW LOGIC: Calculate available quantity (quantityOnHand - quantityAllocated)
      const availableQuantity = item.quantityOnHand - item.quantityAllocated;
      const allocatedFromThisBatch = Math.min(remainingQuantity, availableQuantity);

      // Check for near expiry warning
      let isNearExpiry = false;
      let daysUntilExpiry: number | undefined;
      
      if (item.expiryDate) {
        const timeDiff = item.expiryDate.getTime() - now.getTime();
        daysUntilExpiry = Math.ceil(timeDiff / (1000 * 3600 * 24));
        
        if (daysUntilExpiry <= nearExpiryWarningDays && daysUntilExpiry > 0) {
          isNearExpiry = true;
          warnings.push(
            `Batch ${item.batchNumber || 'N/A'} kedaluwarsa dalam ${daysUntilExpiry} hari (${item.expiryDate.toLocaleDateString('id-ID')})`
          );
        }
      }

      const batch: AllocationBatch = {
        inventoryItemId: item.id,
        batchNumber: item.batchNumber,
        expiryDate: item.expiryDate,
        receivedDate: item.receivedDate,
        availableQuantity,
        allocatedQuantity: allocatedFromThisBatch,
        costPrice: Number(item.costPrice),
        location: item.location,
        isNearExpiry,
        daysUntilExpiry
      };

      batches.push(batch);
      totalCost += allocatedFromThisBatch * Number(item.costPrice);
      totalAllocatedQuantity += allocatedFromThisBatch;
      remainingQuantity -= allocatedFromThisBatch;
    }

    // Check if allocation was successful
    let success = remainingQuantity === 0;
    let finalAllocatedQuantity = totalAllocatedQuantity;
    let finalBatches = batches;
    let finalTotalCost = totalCost;

    if (remainingQuantity > 0) {
      if (allowPartialAllocation) {
        warnings.push(`Alokasi sebagian: ${remainingQuantity} unit tidak dapat dialokasikan karena stok tidak mencukupi`);
        success = totalAllocatedQuantity > 0;
      } else {
        errors.push(`Stok tidak mencukupi: kekurangan ${remainingQuantity} unit dari jumlah yang diminta`);
        success = false;
        // When partial allocation is not allowed and we can't fulfill the full request,
        // don't allocate anything
        finalAllocatedQuantity = 0;
        finalBatches = [];
        finalTotalCost = 0;
      }
    }

    const averageCostPrice = finalAllocatedQuantity > 0 ? finalTotalCost / finalAllocatedQuantity : 0;

    return {
      success,
      allocatedQuantity: finalAllocatedQuantity,
      batches: finalBatches,
      shortfall: remainingQuantity > 0 ? remainingQuantity : undefined,
      warnings,
      errors,
      totalCost: finalTotalCost,
      averageCostPrice
    };
  }

  private async executeAllocation(
    batches: AllocationBatch[],
    productId: string,
    userId: string,
    reason: string,
    notes?: string
  ): Promise<void> {
    await this.prisma.$transaction(async (prisma) => {
      for (const batch of batches) {
        // Update inventory allocation (NEW LOGIC: increment allocated, don't touch quantityOnHand)
        await prisma.inventoryItem.update({
          where: { id: batch.inventoryItemId },
          data: {
            quantityAllocated: { increment: batch.allocatedQuantity },
            updatedBy: userId
          }
        });

        // Create stock movement record
        await prisma.stockMovement.create({
          data: {
            inventoryItemId: batch.inventoryItemId,
            type: StockMovementType.ALLOCATION,
            quantity: batch.allocatedQuantity,
            unitPrice: new Decimal(batch.costPrice),
            referenceType: 'ALLOCATION',
            reason,
            notes: notes || `Dialokasikan ${batch.allocatedQuantity} unit dari batch ${batch.batchNumber || 'N/A'}`,
            createdBy: userId
          }
        });
      }
    });
  }

  private createEmptyAllocationResult(
    productId: string,
    requestedQuantity: number,
    method: AllocationMethod,
    previewOnly: boolean,
    errors: string[]
  ): AllocationResult {
    return {
      success: false,
      productId,
      requestedQuantity,
      allocatedQuantity: 0,
      method,
      batches: [],
      shortfall: requestedQuantity,
      warnings: [],
      errors,
      totalCost: 0,
      averageCostPrice: 0,
      previewOnly
    };
  }

  async getAvailableStockSummary(productId: string): Promise<{
    totalAvailable: number;
    batchCount: number;
    nearExpiryCount: number;
    nearExpiryQuantity: number;
    expiredCount: number;
    expiredQuantity: number;
    oldestBatch?: Date;
    newestBatch?: Date;
    earliestExpiry?: Date;
    unit?: {
      name: string;
      abbreviation: string;
    };
  }> {
    const now = new Date();
    const thirtyDaysFromNow = new Date(now);
    thirtyDaysFromNow.setDate(now.getDate() + 30);

    // NEW LOGIC: Calculate available stock as (quantityOnHand - quantityAllocated)
    const baseWhere = {
      productId,
      isActive: true,
      quantityOnHand: { gt: 0 }
    };

    const [
      totalStats,
      nearExpiryStats,
      expiredStats,
      oldestBatch,
      newestBatch,
      earliestExpiry
    ] = await Promise.all([
      this.prisma.inventoryItem.aggregate({
        where: baseWhere,
        _sum: { quantityOnHand: true, quantityAllocated: true },
        _count: { id: true }
      }),
      this.prisma.inventoryItem.aggregate({
        where: {
          ...baseWhere,
          expiryDate: {
            gte: now,
            lte: thirtyDaysFromNow
          }
        },
        _sum: { quantityOnHand: true, quantityAllocated: true },
        _count: { id: true }
      }),
      this.prisma.inventoryItem.aggregate({
        where: {
          ...baseWhere,
          expiryDate: {
            lt: now
          }
        },
        _sum: { quantityOnHand: true, quantityAllocated: true },
        _count: { id: true }
      }),
      this.prisma.inventoryItem.findFirst({
        where: baseWhere,
        orderBy: { receivedDate: 'asc' },
        select: { receivedDate: true }
      }),
      this.prisma.inventoryItem.findFirst({
        where: baseWhere,
        orderBy: { receivedDate: 'desc' },
        select: { receivedDate: true }
      }),
      this.prisma.inventoryItem.findFirst({
        where: {
          ...baseWhere,
          expiryDate: { not: null, gt: now }
        },
        orderBy: { expiryDate: 'asc' },
        select: { expiryDate: true }
      })
    ]);

    // Calculate available stock as (quantityOnHand - quantityAllocated)
    const totalOnHand = totalStats._sum.quantityOnHand || 0;
    const totalAllocated = totalStats._sum.quantityAllocated || 0;
    const totalAvailable = Math.max(0, totalOnHand - totalAllocated);

    // Calculate quantities for near-expiry and expired categories
    const nearExpiryOnHand = nearExpiryStats._sum.quantityOnHand || 0;
    const nearExpiryAllocated = nearExpiryStats._sum.quantityAllocated || 0;
    const nearExpiryQuantity = Math.max(0, nearExpiryOnHand - nearExpiryAllocated);

    const expiredOnHand = expiredStats._sum.quantityOnHand || 0;
    const expiredAllocated = expiredStats._sum.quantityAllocated || 0;
    const expiredQuantity = Math.max(0, expiredOnHand - expiredAllocated);

    // Get product unit information
    const product = await this.prisma.product.findUnique({
      where: { id: productId },
      include: { baseUnit: true }
    });

    return {
      totalAvailable,
      batchCount: totalStats._count.id || 0,
      nearExpiryCount: nearExpiryStats._count.id || 0,
      nearExpiryQuantity,
      expiredCount: expiredStats._count.id || 0,
      expiredQuantity,
      oldestBatch: oldestBatch?.receivedDate || undefined,
      newestBatch: newestBatch?.receivedDate || undefined,
      earliestExpiry: earliestExpiry?.expiryDate || undefined,
      unit: product?.baseUnit ? {
        name: product.baseUnit.name,
        abbreviation: product.baseUnit.abbreviation
      } : undefined
    };
  }
}
