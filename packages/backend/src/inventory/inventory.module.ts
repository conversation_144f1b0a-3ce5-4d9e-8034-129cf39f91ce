import { Module } from '@nestjs/common';
import { InventoryService } from './inventory.service';
import { InventoryController } from './inventory.controller';
import { StockAllocationService } from './services/stock-allocation.service';
import { AllocationReportService } from './services/allocation-report.service';
import { InventoryReportService } from './services/inventory-report.service';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [InventoryController],
  providers: [InventoryService, StockAllocationService, AllocationReportService, InventoryReportService],
  exports: [InventoryService, StockAllocationService, AllocationReportService, InventoryReportService],
})
export class InventoryModule {}
