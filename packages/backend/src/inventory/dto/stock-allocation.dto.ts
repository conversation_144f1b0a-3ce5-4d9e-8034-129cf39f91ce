import { IsString, IsInt, IsEnum, IsOptional, IsBoolean, Min } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export enum AllocationMethod {
  FIFO = 'FIFO', // First In First Out
  FEFO = 'FEFO', // First Expired First Out
}

export class StockAllocationDto {
  @IsString()
  productId: string;

  @IsInt()
  @Min(1)
  @Type(() => Number)
  @Transform(({ value }) => parseInt(value))
  requestedQuantity: number;

  @IsEnum(AllocationMethod)
  method: AllocationMethod;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  previewOnly?: boolean = false;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  allowPartialAllocation?: boolean = true;

  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  nearExpiryWarningDays?: number = 30;

  @IsOptional()
  @IsString()
  reason?: string;

  @IsOptional()
  @IsString()
  notes?: string;
}

export interface AllocationBatch {
  inventoryItemId: string;
  batchNumber?: string;
  expiryDate?: Date;
  receivedDate: Date;
  availableQuantity: number;
  allocatedQuantity: number;
  costPrice: number;
  location?: string;
  isNearExpiry?: boolean;
  daysUntilExpiry?: number;
}

export interface AllocationResult {
  success: boolean;
  productId: string;
  requestedQuantity: number;
  allocatedQuantity: number;
  method: AllocationMethod;
  batches: AllocationBatch[];
  shortfall?: number;
  warnings: string[];
  errors: string[];
  totalCost: number;
  averageCostPrice: number;
  previewOnly: boolean;
}

export interface AllocationOptions {
  previewOnly?: boolean;
  allowPartialAllocation?: boolean;
  nearExpiryWarningDays?: number;
  respectMinimumStock?: boolean;
  minimumStockLevel?: number;
}
