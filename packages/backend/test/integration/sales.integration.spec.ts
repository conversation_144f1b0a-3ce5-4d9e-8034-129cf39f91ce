import { IntegrationTestSetup, TestContext, expectValidationError, expectUnauthorized, expectForbidden, expectNotFound, expectConflict, expectSuccess } from './test-setup';

describe('Sales Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;
  let testCustomerId: string;
  let testProductId: string;
  let testUnitId: string;
  let testSupplierId: string;

  let testSaleId: string;

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();

    // Create test data needed for sales
    const supplier = await testSetup.createTestSupplier(ctx.users.admin.id);
    testSupplierId = supplier.id;

    const baseUnit = await testSetup.createTestProductUnit(ctx.users.admin.id);
    const product = await testSetup.createTestProduct(baseUnit.id, ctx.users.admin.id);
    testProductId = product.id;
    testUnitId = baseUnit.id;

    await testSetup.createTestInventoryItem(testProductId, testUnitId, testSupplierId, ctx.users.admin.id);

    const customer = await testSetup.createTestCustomer(ctx.users.admin.id);
    testCustomerId = customer.id;
  });

  afterAll(async () => {
    await testSetup.teardown();
  });

  describe('POST /api/sales', () => {
    it('should create sale with registered customer', async () => {
      const saleData = {
        customerId: testCustomerId,
        paymentMethod: 'CASH',
        amountPaid: 20000,
        taxAmount: 1500,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 2,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.customerId).toBe(testCustomerId);
      expect(response.body.cashierId).toBe(ctx.users.cashier.id);
      expect(response.body.status).toBe('COMPLETED');
      expect(response.body.paymentMethod).toBe('CASH');
      expect(response.body.subtotal).toBe(15000); // 2 * 7500
      expect(response.body.taxAmount).toBe(1500);
      expect(response.body.totalAmount).toBe(16500); // 15000 + 1500
      expect(response.body.amountPaid).toBe(20000);
      expect(response.body.changeAmount).toBe(3500); // 20000 - 16500
      expect(response.body.saleNumber).toBeDefined();
      expect(response.body.saleNumber).toMatch(/^TRX-\d{8}-\d{3}$/);

      testSaleId = response.body.id;
    });

    it('should create sale with walk-in customer', async () => {
      const saleData = {
        customerName: 'Walk-in Customer',
        customerPhone: '+62 813 1234 5678',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.customerId).toBeNull();
      expect(response.body.customerName).toBe('Walk-in Customer');
      expect(response.body.customerPhone).toBe('+62 813 1234 5678');
      expect(response.body.subtotal).toBe(7500);
      expect(response.body.totalAmount).toBe(7500);
      expect(response.body.changeAmount).toBe(2500);
    });

    it('should create sale as admin', async () => {
      const saleData = {
        customerName: 'Admin Sale Customer',
        paymentMethod: 'TRANSFER',
        amountPaid: 15000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 2,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.cashierId).toBe(ctx.users.admin.id);
      expect(response.body.paymentMethod).toBe('TRANSFER');
    });

    it('should create sale as pharmacist', async () => {
      const saleData = {
        customerName: 'Pharmacist Sale Customer',
        paymentMethod: 'CASH',
        amountPaid: 8000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.pharmacist.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.cashierId).toBe(ctx.users.pharmacist.id);
    });

    it('should create sale with multiple items', async () => {
      // Create another product for multi-item test
      const baseUnit2 = await testSetup.createTestProductUnit(ctx.users.admin.id);
      const product2 = await testSetup.createTestProduct(baseUnit2.id, ctx.users.admin.id);
      await testSetup.createTestInventoryItem(product2.id, baseUnit2.id, testSupplierId, ctx.users.admin.id);

      const saleData = {
        customerName: 'Multi-item Customer',
        paymentMethod: 'CASH',
        amountPaid: 25000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
          {
            productId: product2.id,
            unitId: baseUnit2.id,
            quantity: 2,
            unitPrice: 5000,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.subtotal).toBe(17500); // 7500 + (2 * 5000)
      expect(response.body.totalAmount).toBe(17500);
      expect(response.body.changeAmount).toBe(7500); // 25000 - 17500
    });

    it('should create sale with item-level discounts', async () => {
      const saleData = {
        customerName: 'Discount Customer',
        paymentMethod: 'CASH',
        amountPaid: 15000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 2,
            unitPrice: 7500,
            discountType: 'PERCENTAGE',
            discountValue: 10, // 10% discount
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.subtotal).toBe(13500); // 15000 - (15000 * 0.1)
      expect(response.body.totalAmount).toBe(13500);
      expect(response.body.changeAmount).toBe(1500);
    });

    it('should create sale with sale-level discount', async () => {
      const saleData = {
        customerName: 'Sale Discount Customer',
        paymentMethod: 'CASH',
        amountPaid: 15000,
        discountType: 'FIXED_AMOUNT',
        discountValue: 2000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 2,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.subtotal).toBe(13000); // 15000 - 2000
      expect(response.body.discountAmount).toBe(2000);
      expect(response.body.totalAmount).toBe(13000);
      expect(response.body.changeAmount).toBe(2000);
    });

    it('should create sale with tax calculation', async () => {
      const saleData = {
        customerName: 'Tax Customer',
        paymentMethod: 'CASH',
        amountPaid: 20000,
        taxAmount: 1650, // 11% tax
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 2,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.subtotal).toBe(15000);
      expect(response.body.taxAmount).toBe(1650);
      expect(response.body.totalAmount).toBe(16650); // 15000 + 1650
      expect(response.body.changeAmount).toBe(3350); // 20000 - 16650
    });

    it('should auto-generate sale number if not provided', async () => {
      const saleData = {
        customerName: 'Auto Number Customer',
        paymentMethod: 'CASH',
        amountPaid: 8000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.saleNumber).toBeDefined();
      expect(response.body.saleNumber).toMatch(/^TRX-\d{8}-\d{3}$/);
    });

    it('should accept custom sale number', async () => {
      const saleData = {
        saleNumber: 'CUSTOM-TRX-001',
        customerName: 'Custom Number Customer',
        paymentMethod: 'CASH',
        amountPaid: 8000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.saleNumber).toBe('CUSTOM-TRX-001');
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .post('/api/sales')
        .send({
          customerName: 'Test',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
        });

      expectUnauthorized(response);
    });

    it('should fail with duplicate sale number', async () => {
      const saleData = {
        saleNumber: 'CUSTOM-TRX-001', // Already exists
        customerName: 'Duplicate Number Customer',
        paymentMethod: 'CASH',
        amountPaid: 8000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Nomor transaksi sudah digunakan');
    });

    it('should fail with insufficient payment', async () => {
      const saleData = {
        customerName: 'Insufficient Payment Customer',
        paymentMethod: 'CASH',
        amountPaid: 5000, // Less than total
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Jumlah pembayaran tidak mencukupi');
    });

    it('should fail with non-existent product', async () => {
      const saleData = {
        customerName: 'Non-existent Product Customer',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        items: [
          {
            productId: 'non-existent-product-id',
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expect(response.status).toBe(404);
      expect(response.body.message).toContain('Produk tidak ditemukan');
    });

    it('should fail with insufficient stock', async () => {
      const saleData = {
        customerName: 'Insufficient Stock Customer',
        paymentMethod: 'CASH',
        amountPaid: 100000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 200, // More than available stock (100)
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Stok tidak mencukupi');
    });

    it('should fail with missing required fields', async () => {
      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send({});

      expectValidationError(response);
    });

    it('should fail with invalid payment method', async () => {
      const saleData = {
        customerName: 'Invalid Payment Customer',
        paymentMethod: 'INVALID_METHOD',
        amountPaid: 10000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectValidationError(response);
    });

    it('should fail with negative quantity', async () => {
      const saleData = {
        customerName: 'Negative Quantity Customer',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: -1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectValidationError(response);
    });

    it('should fail with negative unit price', async () => {
      const saleData = {
        customerName: 'Negative Price Customer',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: -7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectValidationError(response);
    });

    it('should fail with negative amount paid', async () => {
      const saleData = {
        customerName: 'Negative Payment Customer',
        paymentMethod: 'CASH',
        amountPaid: -10000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectValidationError(response);
    });

    it('should fail with empty items array', async () => {
      const saleData = {
        customerName: 'Empty Items Customer',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        items: [],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectValidationError(response);
    });

    it('should fail with invalid date format', async () => {
      const saleData = {
        customerName: 'Invalid Date Customer',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        saleDate: 'invalid-date',
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectValidationError(response);
    });
  });

  describe('GET /api/sales', () => {
    beforeAll(async () => {
      // Create additional test sales for filtering tests
      const sales = [
        {
          customerName: 'Filter Test Customer 1',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
        },
        {
          customerName: 'Filter Test Customer 2',
          paymentMethod: 'TRANSFER',
          amountPaid: 15000,
          items: [{ productId: testProductId, unitId: testUnitId, quantity: 2, unitPrice: 7500 }],
        },
        {
          customerId: testCustomerId,
          paymentMethod: 'CASH',
          amountPaid: 8000,
          items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
        },
      ];

      for (const sale of sales) {
        await ctx.request
          .post('/api/sales')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(sale);
      }
    });

    it('should list all sales for authenticated user', async () => {
      const response = await ctx.request
        .get('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.meta).toBeDefined();
      expect(response.body.meta.total).toBeGreaterThan(0);
      expect(response.body.meta.page).toBe(1);
      expect(response.body.meta.limit).toBe(10);

      // Check that sales include related data
      const sale = response.body.data[0];
      expect(sale.cashier).toBeDefined();
      expect(sale.saleItems).toBeDefined();
      expect(sale.saleItems).toBeInstanceOf(Array);
    });

    it('should filter sales by payment method', async () => {
      const response = await ctx.request
        .get('/api/sales?paymentMethod=TRANSFER')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((s: any) => s.paymentMethod === 'TRANSFER')).toBe(true);
    });

    it('should filter sales by status', async () => {
      const response = await ctx.request
        .get('/api/sales?status=COMPLETED')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((s: any) => s.status === 'COMPLETED')).toBe(true);
    });

    it('should filter sales by customer', async () => {
      const response = await ctx.request
        .get(`/api/sales?customerId=${testCustomerId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((s: any) => s.customerId === testCustomerId)).toBe(true);
    });

    it('should filter sales by cashier', async () => {
      const response = await ctx.request
        .get(`/api/sales?cashierId=${ctx.users.admin.id}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.every((s: any) => s.cashierId === ctx.users.admin.id)).toBe(true);
    });

    it('should search sales by sale number', async () => {
      const response = await ctx.request
        .get('/api/sales?search=TRX')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.some((s: any) => s.saleNumber.includes('TRX'))).toBe(true);
    });

    it('should search sales by customer name', async () => {
      const response = await ctx.request
        .get('/api/sales?search=Filter Test Customer')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.some((s: any) => s.customerName && s.customerName.includes('Filter Test Customer'))).toBe(true);
    });

    it('should filter sales by date range', async () => {
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const response = await ctx.request
        .get(`/api/sales?startDate=${yesterday.toISOString()}&endDate=${tomorrow.toISOString()}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data).toBeInstanceOf(Array);
    });

    it('should paginate results', async () => {
      const response = await ctx.request
        .get('/api/sales?page=1&limit=2')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data.length).toBeLessThanOrEqual(2);
      expect(response.body.meta.page).toBe(1);
      expect(response.body.meta.limit).toBe(2);
    });

    it('should sort sales', async () => {
      const response = await ctx.request
        .get('/api/sales?sortBy=totalAmount&sortOrder=asc')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      const amounts = response.body.data.map((s: any) => parseFloat(s.totalAmount));
      const sortedAmounts = [...amounts].sort((a, b) => a - b);
      expect(amounts).toEqual(sortedAmounts);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/sales');

      expectUnauthorized(response);
    });

    it('should validate pagination parameters', async () => {
      const response = await ctx.request
        .get('/api/sales?page=0&limit=101')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectValidationError(response);
    });
  });

  describe('GET /api/sales/stats', () => {
    it('should return sales statistics for authenticated user', async () => {
      const response = await ctx.request
        .get('/api/sales/stats')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body).toHaveProperty('totalSales');
      expect(response.body).toHaveProperty('todaySales');
      expect(response.body).toHaveProperty('completedSales');
      expect(response.body).toHaveProperty('totalRevenue');
      expect(response.body).toHaveProperty('todayRevenue');
      expect(typeof response.body.totalSales).toBe('number');
      expect(typeof response.body.todaySales).toBe('number');
      expect(typeof response.body.completedSales).toBe('number');
      expect(typeof response.body.totalRevenue).toBe('number');
      expect(typeof response.body.todayRevenue).toBe('number');
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/sales/stats');

      expectUnauthorized(response);
    });
  });

  describe('GET /api/sales/generate-number', () => {
    it('should generate sale number for authenticated user', async () => {
      const response = await ctx.request
        .get('/api/sales/generate-number')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.saleNumber).toBeDefined();
      expect(response.body.saleNumber).toMatch(/^TRX-\d{8}-\d{3}$/);
    });

    it('should generate unique sale numbers', async () => {
      const response1 = await ctx.request
        .get('/api/sales/generate-number')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      // Add a small delay to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 100));

      const response2 = await ctx.request
        .get('/api/sales/generate-number')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response1);
      expectSuccess(response2);
      expect(response1.body.saleNumber).not.toBe(response2.body.saleNumber);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/sales/generate-number');

      expectUnauthorized(response);
    });
  });

  describe('GET /api/sales/validate-number/:number', () => {
    it('should validate unique sale number', async () => {
      const response = await ctx.request
        .get('/api/sales/validate-number/UNIQUE-TRX-123')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.isUnique).toBe(true);
    });

    it('should validate existing sale number', async () => {
      const response = await ctx.request
        .get('/api/sales/validate-number/CUSTOM-TRX-001')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.isUnique).toBe(false);
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get('/api/sales/validate-number/TEST-123');

      expectUnauthorized(response);
    });
  });

  describe('GET /api/sales/:id', () => {
    it('should return sale details for authenticated user', async () => {
      const response = await ctx.request
        .get(`/api/sales/${testSaleId}`)
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

      expectSuccess(response);
      expect(response.body.id).toBe(testSaleId);
      expect(response.body.customerId).toBe(testCustomerId);
      expect(response.body.cashier).toBeDefined();
      expect(response.body.customer).toBeDefined();
      expect(response.body.saleItems).toBeDefined();
      expect(response.body.saleItems).toBeInstanceOf(Array);
      expect(response.body.saleItems.length).toBeGreaterThan(0);

      // Check sale item details
      const saleItem = response.body.saleItems[0];
      expect(saleItem.product).toBeDefined();
      expect(saleItem.unit).toBeDefined();
      expect(saleItem.quantity).toBeDefined();
      expect(saleItem.unitPrice).toBeDefined();
      expect(saleItem.totalPrice).toBeDefined();
    });

    it('should fail with non-existent sale ID', async () => {
      const response = await ctx.request
        .get('/api/sales/non-existent-id')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectNotFound(response);
      expect(response.body.message).toContain('Transaksi tidak ditemukan');
    });

    it('should fail without authentication', async () => {
      const response = await ctx.request
        .get(`/api/sales/${testSaleId}`);

      expectUnauthorized(response);
    });
  });

  describe('Payment Method Tests', () => {
    it('should create sale with CASH payment', async () => {
      const saleData = {
        customerName: 'Cash Payment Customer',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.paymentMethod).toBe('CASH');
    });

    it('should create sale with TRANSFER payment', async () => {
      const saleData = {
        customerName: 'Transfer Payment Customer',
        paymentMethod: 'TRANSFER',
        amountPaid: 7500,
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.paymentMethod).toBe('TRANSFER');
      expect(response.body.changeAmount).toBe(0); // Exact payment
    });

    it('should create sale with CREDIT payment', async () => {
      const saleData = {
        customerName: 'Credit Payment Customer',
        paymentMethod: 'CREDIT',
        amountPaid: 7500,
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.paymentMethod).toBe('CREDIT');
    });

    it('should create sale with GIRO payment', async () => {
      const saleData = {
        customerName: 'Giro Payment Customer',
        paymentMethod: 'GIRO',
        amountPaid: 7500,
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.paymentMethod).toBe('GIRO');
    });
  });

  describe('Discount Calculation Tests', () => {
    it('should calculate percentage discount correctly', async () => {
      const saleData = {
        customerName: 'Percentage Discount Customer',
        paymentMethod: 'CASH',
        amountPaid: 20000,
        discountType: 'PERCENTAGE',
        discountValue: 20, // 20% discount
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 2, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.subtotal).toBe(12000); // 15000 - (15000 * 0.2)
      expect(response.body.discountAmount).toBe(3000); // 15000 * 0.2
      expect(response.body.totalAmount).toBe(12000);
    });

    it('should calculate fixed amount discount correctly', async () => {
      const saleData = {
        customerName: 'Fixed Discount Customer',
        paymentMethod: 'CASH',
        amountPaid: 20000,
        discountType: 'FIXED_AMOUNT',
        discountValue: 2500,
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 2, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.subtotal).toBe(12500); // 15000 - 2500
      expect(response.body.discountAmount).toBe(2500);
      expect(response.body.totalAmount).toBe(12500);
    });

    it('should calculate item-level percentage discount correctly', async () => {
      const saleData = {
        customerName: 'Item Percentage Discount Customer',
        paymentMethod: 'CASH',
        amountPaid: 20000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 2,
            unitPrice: 7500,
            discountType: 'PERCENTAGE',
            discountValue: 15, // 15% item discount
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.subtotal).toBe(12750); // 15000 - (15000 * 0.15)
      expect(response.body.totalAmount).toBe(12750);
    });

    it('should calculate item-level fixed discount correctly', async () => {
      const saleData = {
        customerName: 'Item Fixed Discount Customer',
        paymentMethod: 'CASH',
        amountPaid: 20000,
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 2,
            unitPrice: 7500,
            discountType: 'FIXED_AMOUNT',
            discountValue: 1000, // 1000 item discount
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.subtotal).toBe(14000); // 15000 - 1000
      expect(response.body.totalAmount).toBe(14000);
    });
  });

  describe('Inventory Integration Tests', () => {
    it('should allocate stock correctly during sale', async () => {
      // Get initial inventory
      const initialInventory = await ctx.request
        .get(`/api/inventory?productId=${testProductId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      const initialStock = initialInventory.body.data.reduce((sum: number, item: any) => sum + (item.quantityOnHand - item.quantityAllocated), 0);

      const saleData = {
        customerName: 'Stock Allocation Customer',
        paymentMethod: 'CASH',
        amountPaid: 40000, // 5 * 7500 = 37500, paying 40000
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 5, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);

      // Check that stock was allocated
      const finalInventory = await ctx.request
        .get(`/api/inventory?productId=${testProductId}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      // Calculate available stock (quantityOnHand - quantityAllocated) for both initial and final
      const finalAvailableStock = finalInventory.body.data.reduce((sum: number, item: any) => sum + (item.quantityOnHand - item.quantityAllocated), 0);
      expect(finalAvailableStock).toBe(initialStock - 5);
    });

    it('should use FEFO allocation for medicine products', async () => {
      // Create a medicine product
      const medicineBaseUnit = await testSetup.createTestProductUnit(ctx.users.admin.id);
      const medicineProduct = await testSetup.createTestProduct(medicineBaseUnit.id, ctx.users.admin.id, 'MEDICINE');

      // Create inventory items with different expiry dates
      const earlyBatchResponse = await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          productId: medicineProduct.id,
          unitId: medicineBaseUnit.id,
          supplierId: testSupplierId,
          batchNumber: 'BATCH-EARLY',
          quantityOnHand: 50,
          costPrice: 5000,
          sellingPrice: 7500,
          expiryDate: '2025-06-30', // Earlier expiry (but still in future)
          location: 'Rak A-1',
        });

      expectSuccess(earlyBatchResponse, 201);

      const lateBatchResponse = await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          productId: medicineProduct.id,
          unitId: medicineBaseUnit.id,
          supplierId: testSupplierId,
          batchNumber: 'BATCH-LATE',
          quantityOnHand: 50,
          costPrice: 5000,
          sellingPrice: 7500,
          expiryDate: '2025-12-31', // Later expiry
          location: 'Rak A-2',
        });

      expectSuccess(lateBatchResponse, 201);

      const saleData = {
        customerName: 'FEFO Test Customer',
        paymentMethod: 'CASH',
        amountPaid: 80000, // 10 * 7500 = 75000, paying 80000
        items: [{ productId: medicineProduct.id, unitId: medicineBaseUnit.id, quantity: 10, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);

      // Verify FEFO allocation by checking which inventory items were allocated from
      // Get all inventory items for this medicine product
      const inventoryItems = await ctx.request
        .get(`/api/inventory?productId=${medicineProduct.id}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(inventoryItems);
      expect(inventoryItems.body.data.length).toBe(2); // Should have 2 batches

      // Find the batches by their batch numbers
      const earlyBatch = inventoryItems.body.data.find((item: any) => item.batchNumber === 'BATCH-EARLY');
      const lateBatch = inventoryItems.body.data.find((item: any) => item.batchNumber === 'BATCH-LATE');

      expect(earlyBatch).toBeDefined();
      expect(lateBatch).toBeDefined();

      // Check stock movements for both batches to verify FEFO allocation
      const earlyBatchMovements = await ctx.request
        .get(`/api/inventory/${earlyBatch.id}/stock-movements`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      const lateBatchMovements = await ctx.request
        .get(`/api/inventory/${lateBatch.id}/stock-movements`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(earlyBatchMovements);
      expectSuccess(lateBatchMovements);

      // Find allocation movements (sales create ALLOCATION type movements)
      const earlyAllocationMovements = earlyBatchMovements.body.data.filter((m: any) =>
        m.type === 'ALLOCATION' && m.reason.includes('Penjualan')
      );
      const lateAllocationMovements = lateBatchMovements.body.data.filter((m: any) =>
        m.type === 'ALLOCATION' && m.reason.includes('Penjualan')
      );

      // FEFO should prioritize the earlier expiry batch (BATCH-EARLY)
      // Since we're buying 10 items and each batch has 50, only the early batch should be allocated from
      expect(earlyAllocationMovements.length).toBeGreaterThan(0);
      expect(earlyAllocationMovements[0].quantity).toBe(10);

      // Late batch should not be allocated from since early batch has enough stock
      expect(lateAllocationMovements.length).toBe(0);

      // Verify the sale was created successfully
      expect(response.body.id).toBeDefined();
      expect(response.body.totalAmount).toBe(75000);
    });

    it('should use FIFO allocation for non-medicine products', async () => {
      // Create a non-medicine product
      const nonMedicineBaseUnit = await testSetup.createTestProductUnit(ctx.users.admin.id);
      const nonMedicineProduct = await testSetup.createTestProduct(nonMedicineBaseUnit.id, ctx.users.admin.id, 'SUPPLEMENT');

      // Create inventory items with different received dates
      await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          productId: nonMedicineProduct.id,
          unitId: nonMedicineBaseUnit.id,
          supplierId: testSupplierId,
          batchNumber: 'BATCH-OLD',
          quantityOnHand: 50,
          costPrice: 5000,
          sellingPrice: 7500,
          receivedDate: '2024-01-01', // Earlier received
          location: 'Rak B-1',
        });

      await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          productId: nonMedicineProduct.id,
          unitId: nonMedicineBaseUnit.id,
          supplierId: testSupplierId,
          batchNumber: 'BATCH-NEW',
          quantityOnHand: 50,
          costPrice: 5000,
          sellingPrice: 7500,
          receivedDate: '2024-06-01', // Later received
          location: 'Rak B-2',
        });

      const saleData = {
        customerName: 'FIFO Test Customer',
        paymentMethod: 'CASH',
        amountPaid: 80000, // 10 * 7500 = 75000, paying 80000
        items: [{ productId: nonMedicineProduct.id, unitId: nonMedicineBaseUnit.id, quantity: 10, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);

      // Verify FIFO allocation by checking which inventory items were allocated from
      // Get all inventory items for this non-medicine product
      const inventoryItems = await ctx.request
        .get(`/api/inventory?productId=${nonMedicineProduct.id}`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(inventoryItems);
      expect(inventoryItems.body.data.length).toBe(2); // Should have 2 batches

      // Find the batches by their batch numbers
      const oldBatch = inventoryItems.body.data.find((item: any) => item.batchNumber === 'BATCH-OLD');
      const newBatch = inventoryItems.body.data.find((item: any) => item.batchNumber === 'BATCH-NEW');

      expect(oldBatch).toBeDefined();
      expect(newBatch).toBeDefined();

      // Check stock movements for both batches to verify FIFO allocation
      const oldBatchMovements = await ctx.request
        .get(`/api/inventory/${oldBatch.id}/stock-movements`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      const newBatchMovements = await ctx.request
        .get(`/api/inventory/${newBatch.id}/stock-movements`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(oldBatchMovements);
      expectSuccess(newBatchMovements);

      // Find allocation movements (sales create ALLOCATION type movements)
      const oldAllocationMovements = oldBatchMovements.body.data.filter((m: any) =>
        m.type === 'ALLOCATION' && m.reason.includes('Penjualan')
      );
      const newAllocationMovements = newBatchMovements.body.data.filter((m: any) =>
        m.type === 'ALLOCATION' && m.reason.includes('Penjualan')
      );

      // FIFO should prioritize the earlier received batch (BATCH-OLD)
      // Since we're buying 10 items and each batch has 50, only the old batch should be allocated from
      expect(oldAllocationMovements.length).toBeGreaterThan(0);
      expect(oldAllocationMovements[0].quantity).toBe(10);

      // New batch should not be allocated from since old batch has enough stock
      expect(newAllocationMovements.length).toBe(0);

      // Verify the sale was created successfully
      expect(response.body.id).toBeDefined();
      expect(response.body.totalAmount).toBe(75000);
    });
  });

  describe('Edge Cases and Boundary Conditions', () => {
    it('should handle very large quantities', async () => {
      // Create inventory with large quantity
      await ctx.request
        .post('/api/inventory')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send({
          productId: testProductId,
          unitId: testUnitId,
          supplierId: testSupplierId,
          batchNumber: 'LARGE-BATCH',
          quantityOnHand: 10000,
          costPrice: 5000,
          sellingPrice: 7500,
          location: 'Rak C-1',
        });

      const saleData = {
        customerName: 'Large Quantity Customer',
        paymentMethod: 'CASH',
        amountPaid: 1000000,
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 100, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.subtotal).toBe(750000); // 100 * 7500
    });

    it('should handle very large amounts', async () => {
      const saleData = {
        customerName: 'Large Amount Customer',
        paymentMethod: 'TRANSFER',
        amountPaid: 999999.99,
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 999999.99 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.totalAmount).toBe(999999.99);
      expect(response.body.changeAmount).toBe(0);
    });

    it('should handle decimal quantities correctly', async () => {
      const saleData = {
        customerName: 'Decimal Quantity Customer',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500.50 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.subtotal).toBe(7500.50);
      expect(response.body.totalAmount).toBe(7500.50);
      expect(response.body.changeAmount).toBe(2499.50);
    });

    it('should handle special characters in customer data', async () => {
      const saleData = {
        customerName: 'José María Ñoño',
        customerPhone: '+62 (812) 3456-7890',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        notes: 'Special chars: àáâãäåæçèéêë',
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.customerName).toBe('José María Ñoño');
      expect(response.body.customerPhone).toBe('+62 (812) 3456-7890');
      expect(response.body.notes).toBe('Special chars: àáâãäåæçèéêë');
    });

    it('should handle empty search queries gracefully', async () => {
      const response = await ctx.request
        .get('/api/sales?search=')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data).toBeInstanceOf(Array);
    });

    it('should handle large page numbers gracefully', async () => {
      const response = await ctx.request
        .get('/api/sales?page=999999&limit=10')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data.length).toBe(0);
      expect(response.body.meta.page).toBe(999999);
    });

    it('should handle exact payment amounts', async () => {
      const saleData = {
        customerName: 'Exact Payment Customer',
        paymentMethod: 'CASH',
        amountPaid: 7500, // Exact amount
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.changeAmount).toBe(0);
    });

    it('should handle sales with notes', async () => {
      const saleData = {
        customerName: 'Notes Customer',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        notes: 'Customer requested special packaging',
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 1,
            unitPrice: 7500,
            notes: 'Handle with care',
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(response.body.notes).toBe('Customer requested special packaging');
    });

    it('should handle future sale dates', async () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);

      const saleData = {
        customerName: 'Future Date Customer',
        paymentMethod: 'CASH',
        amountPaid: 10000,
        saleDate: futureDate.toISOString(),
        items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      expectSuccess(response, 201);
      expect(new Date(response.body.saleDate).getTime()).toBe(futureDate.getTime());
    });
  });

  // Enhanced Transaction Processing Tests
  describe('Enhanced Transaction Processing', () => {
    let draftSaleId: string;

    describe('POST /api/sales/draft', () => {
      it('should verify draft endpoint exists', async () => {
        // First, let's test with a simple GET to see what happens
        const getResponse = await ctx.request
          .get('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        // Now test the actual POST
        const postResponse = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send({});

        // The endpoint should exist (not 404), even if it fails validation
        expect(postResponse.status).not.toBe(404);
      });

      it('should create draft sale without stock allocation', async () => {
        const saleData = {
          customerName: 'Draft Sale Customer',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        };

        const response = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(saleData);

        expectSuccess(response, 201);
        expect(response.body.status).toBe('DRAFT');
        expect(response.body.customerName).toBe('Draft Sale Customer');
        expect(response.body.subtotal).toBe(7500);
        expect(response.body.totalAmount).toBe(7500);
        expect(response.body.changeAmount).toBe(2500);
        expect(response.body.saleNumber).toBeDefined();

        draftSaleId = response.body.id;
      });

      it('should create draft sale with registered customer', async () => {
        const saleData = {
          customerId: testCustomerId,
          paymentMethod: 'TRANSFER',
          amountPaid: 15000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 2,
              unitPrice: 7500,
            },
          ],
        };

        const response = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(saleData);

        expectSuccess(response, 201);
        expect(response.body.status).toBe('DRAFT');
        expect(response.body.customerId).toBe(testCustomerId);
        expect(response.body.subtotal).toBe(15000);
        expect(response.body.paymentMethod).toBe('TRANSFER');
      });

      it('should create draft sale with discounts', async () => {
        const saleData = {
          customerName: 'Draft Discount Customer',
          paymentMethod: 'CASH',
          amountPaid: 20000,
          discountType: 'PERCENTAGE',
          discountValue: 10,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 2,
              unitPrice: 7500,
              discountType: 'FIXED',
              discountValue: 500,
            },
          ],
        };

        const response = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(saleData);

        expectSuccess(response, 201);
        expect(response.body.status).toBe('DRAFT');
        expect(response.body.discountAmount).toBeGreaterThan(0);
      });

      it('should allow draft sale even with insufficient stock (warning only)', async () => {
        const saleData = {
          customerName: 'High Quantity Draft Customer',
          paymentMethod: 'CASH',
          amountPaid: 1000000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 500, // More than available stock
              unitPrice: 7500,
            },
          ],
        };

        const response = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(saleData);

        expectSuccess(response, 201);
        expect(response.body.status).toBe('DRAFT');
        expect(response.body.subtotal).toBe(3750000); // 500 * 7500
      });

      it('should allow draft sale with insufficient payment (warning only)', async () => {
        const saleData = {
          customerName: 'Insufficient Payment Draft Customer',
          paymentMethod: 'CASH',
          amountPaid: 5000, // Less than total (7500)
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        };

        const response = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(saleData);

        // Draft sales should allow insufficient payment
        expectSuccess(response, 201);
        expect(response.body.status).toBe('DRAFT');
        expect(response.body.totalAmount).toBe(7500);
        expect(response.body.amountPaid).toBe(5000);
        expect(response.body.changeAmount).toBe(-2500); // Negative change indicates insufficient payment
      });

      it('should fail without authentication', async () => {
        const response = await ctx.request
          .post('/api/sales/draft')
          .send({
            customerName: 'Test',
            paymentMethod: 'CASH',
            amountPaid: 10000,
            items: [{ productId: testProductId, unitId: testUnitId, quantity: 1, unitPrice: 7500 }],
          });

        expectUnauthorized(response);
      });
    });

    describe('PATCH /api/sales/:id/complete', () => {
      it('should complete draft sale with stock allocation', async () => {
        const response = await ctx.request
          .patch(`/api/sales/${draftSaleId}/complete`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectSuccess(response);
        expect(response.body.status).toBe('COMPLETED');
        expect(response.body.id).toBe(draftSaleId);
      });

      it('should fail to complete already completed sale', async () => {
        const response = await ctx.request
          .patch(`/api/sales/${draftSaleId}/complete`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectConflict(response);
        expect(response.body.message).toContain('Hanya transaksi draft yang dapat diselesaikan');
      });

      it('should fail to complete sale with insufficient stock', async () => {
        // First, check current available stock to ensure we use a quantity that exceeds it
        const inventoryResponse = await ctx.request
          .get(`/api/inventory?productId=${testProductId}`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        const availableStock = inventoryResponse.body.data.reduce(
          (sum: number, item: any) => sum + (item.quantityOnHand - item.quantityAllocated),
          0
        );

        // Use a quantity that definitely exceeds available stock
        const excessiveQuantity = availableStock + 50;

        // Create a draft sale with excessive quantity
        const draftData = {
          customerName: 'High Stock Draft Customer',
          paymentMethod: 'CASH',
          amountPaid: 1000000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: excessiveQuantity, // Definitely more than available stock
              unitPrice: 7500,
            },
          ],
        };

        const draftResponse = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(draftData);

        expectSuccess(draftResponse, 201);

        // Try to complete it - this should fail due to insufficient stock
        const completeResponse = await ctx.request
          .patch(`/api/sales/${draftResponse.body.id}/complete`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expect(completeResponse.status).toBe(400);
        expect(completeResponse.body.message).toContain('Stok tidak mencukupi');
      });

      it('should fail with non-existent sale ID', async () => {
        const response = await ctx.request
          .patch('/api/sales/non-existent-id/complete')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectNotFound(response);
      });

      it('should fail without authentication', async () => {
        const response = await ctx.request
          .patch(`/api/sales/${draftSaleId}/complete`);

        expectUnauthorized(response);
      });
    });

    describe('PATCH /api/sales/:id/cancel', () => {
      let cancelTestSaleId: string;

      beforeAll(async () => {
        // Create a completed sale for cancellation tests
        const saleData = {
          customerName: 'Cancel Test Customer',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        };

        const response = await ctx.request
          .post('/api/sales')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(saleData);

        cancelTestSaleId = response.body.id;
      });

      it('should cancel completed sale', async () => {
        const response = await ctx.request
          .patch(`/api/sales/${cancelTestSaleId}/cancel`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send({ reason: 'Customer requested cancellation' });

        expectSuccess(response);
        expect(response.body.status).toBe('CANCELLED');
        expect(response.body.notes).toContain('Dibatalkan: Customer requested cancellation');
      });

      it('should restore stock when cancelling completed sale', async () => {
        // Create a new completed sale for stock restoration testing
        const saleData = {
          customerName: 'Stock Restoration Test Customer',
          paymentMethod: 'CASH',
          amountPaid: 15000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 2,
              unitPrice: 7500,
            },
          ],
        };

        const saleResponse = await ctx.request
          .post('/api/sales')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(saleData);

        const saleId = saleResponse.body.id;

        // Get inventory before cancellation
        const inventoryBefore = await ctx.request
          .get(`/api/inventory?productId=${testProductId}`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        const allocatedBefore = inventoryBefore.body.data.reduce((sum: number, item: any) => sum + item.quantityAllocated, 0);

        // Cancel the sale
        const cancelResponse = await ctx.request
          .patch(`/api/sales/${saleId}/cancel`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send({ reason: 'Stock restoration test' });

        expectSuccess(cancelResponse);

        // Get inventory after cancellation
        const inventoryAfter = await ctx.request
          .get(`/api/inventory?productId=${testProductId}`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        const allocatedAfter = inventoryAfter.body.data.reduce((sum: number, item: any) => sum + item.quantityAllocated, 0);

        // Verify stock was restored (allocated quantity decreased by 2)
        expect(allocatedAfter).toBe(allocatedBefore - 2);

        // Verify stock movement was created for deallocation
        // Check all inventory items for the product since deallocation might happen on any item
        let allDeallocationMovements: any[] = [];

        for (const inventoryItem of inventoryAfter.body.data) {
          const stockMovements = await ctx.request
            .get(`/api/inventory/${inventoryItem.id}/stock-movements`)
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

          expectSuccess(stockMovements);

          // Find deallocation movements for this item
          const itemDeallocationMovements = stockMovements.body.data.filter((movement: any) =>
            movement.type === 'ALLOCATION' &&
            movement.quantity < 0 &&
            movement.referenceType === 'SALE_CANCELLATION' &&
            movement.referenceId === saleId
          );

          allDeallocationMovements.push(...itemDeallocationMovements);
        }

        expect(allDeallocationMovements.length).toBeGreaterThan(0);

        // Verify total deallocated quantity
        const totalDeallocated = allDeallocationMovements.reduce((sum: number, movement: any) => sum + Math.abs(movement.quantity), 0);
        expect(totalDeallocated).toBe(2);

        // Verify movement details
        expect(allDeallocationMovements[0].referenceId).toBe(saleId);
        expect(allDeallocationMovements[0].reason).toContain('Pembatalan transaksi');
      });

      it('should fail to cancel already cancelled sale', async () => {
        const response = await ctx.request
          .patch(`/api/sales/${cancelTestSaleId}/cancel`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send({ reason: 'Another reason' });

        expectConflict(response);
        expect(response.body.message).toContain('Transaksi sudah dibatalkan');
      });

      it('should cancel draft sale', async () => {
        // Create a draft sale
        const draftData = {
          customerName: 'Draft Cancel Customer',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        };

        const draftResponse = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(draftData);

        // Cancel the draft
        const cancelResponse = await ctx.request
          .patch(`/api/sales/${draftResponse.body.id}/cancel`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send({ reason: 'Draft cancellation' });

        expectSuccess(cancelResponse);
        expect(cancelResponse.body.status).toBe('CANCELLED');
      });

      it('should fail with non-existent sale ID', async () => {
        const response = await ctx.request
          .patch('/api/sales/non-existent-id/cancel')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send({ reason: 'Test' });

        expectNotFound(response);
      });

      it('should fail without authentication', async () => {
        const response = await ctx.request
          .patch(`/api/sales/${cancelTestSaleId}/cancel`)
          .send({ reason: 'Test' });

        expectUnauthorized(response);
      });
    });

    describe('PATCH /api/sales/:id/refund', () => {
      let refundTestSaleId: string;

      beforeAll(async () => {
        // Create a completed sale for refund tests
        const saleData = {
          customerName: 'Refund Test Customer',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        };

        const response = await ctx.request
          .post('/api/sales')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(saleData);

        refundTestSaleId = response.body.id;
      });

      it('should refund completed sale as manager', async () => {
        const response = await ctx.request
          .patch(`/api/sales/${refundTestSaleId}/refund`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({ reason: 'Product defect' });

        expectSuccess(response);
        expect(response.body.status).toBe('REFUNDED');
        expect(response.body.notes).toContain('Di-refund: Product defect');
      });

      it('should restore stock when refunding completed sale', async () => {
        // Create a new completed sale for refund stock restoration testing
        const saleData = {
          customerName: 'Refund Stock Restoration Test Customer',
          paymentMethod: 'CASH',
          amountPaid: 22500,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 3,
              unitPrice: 7500,
            },
          ],
        };

        const saleResponse = await ctx.request
          .post('/api/sales')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(saleData);

        const saleId = saleResponse.body.id;

        // Get inventory before refund
        const inventoryBefore = await ctx.request
          .get(`/api/inventory?productId=${testProductId}`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        const allocatedBefore = inventoryBefore.body.data.reduce((sum: number, item: any) => sum + item.quantityAllocated, 0);

        // Refund the sale
        const refundResponse = await ctx.request
          .patch(`/api/sales/${saleId}/refund`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({ reason: 'Stock restoration test refund' });

        expectSuccess(refundResponse);

        // Get inventory after refund
        const inventoryAfter = await ctx.request
          .get(`/api/inventory?productId=${testProductId}`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

        const allocatedAfter = inventoryAfter.body.data.reduce((sum: number, item: any) => sum + item.quantityAllocated, 0);

        // Verify stock was restored (allocated quantity decreased by 3)
        expect(allocatedAfter).toBe(allocatedBefore - 3);

        // Verify stock movement was created for deallocation
        // Check all inventory items for the product since deallocation might happen on any item
        let allRefundDeallocationMovements: any[] = [];

        for (const inventoryItem of inventoryAfter.body.data) {
          const stockMovements = await ctx.request
            .get(`/api/inventory/${inventoryItem.id}/stock-movements`)
            .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

          expectSuccess(stockMovements);

          // Find deallocation movements for this item
          const itemDeallocationMovements = stockMovements.body.data.filter((movement: any) =>
            movement.type === 'ALLOCATION' &&
            movement.quantity < 0 &&
            movement.referenceType === 'SALE_REFUND' &&
            movement.referenceId === saleId
          );

          allRefundDeallocationMovements.push(...itemDeallocationMovements);
        }

        expect(allRefundDeallocationMovements.length).toBeGreaterThan(0);

        // Verify total deallocated quantity
        const totalDeallocated = allRefundDeallocationMovements.reduce((sum: number, movement: any) => sum + Math.abs(movement.quantity), 0);
        expect(totalDeallocated).toBe(3);

        // Verify movement details
        expect(allRefundDeallocationMovements[0].referenceId).toBe(saleId);
        expect(allRefundDeallocationMovements[0].reason).toContain('Refund transaksi');
      });

      it('should fail to refund non-completed sale', async () => {
        // Create a draft sale
        const draftData = {
          customerName: 'Draft Refund Customer',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        };

        const draftResponse = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(draftData);

        // Try to refund the draft
        const refundResponse = await ctx.request
          .patch(`/api/sales/${draftResponse.body.id}/refund`)
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({ reason: 'Test' });

        expectConflict(refundResponse);
        expect(refundResponse.body.message).toContain('Hanya transaksi yang sudah selesai yang dapat di-refund');
      });

      it('should fail as non-manager user', async () => {
        // Create another completed sale
        const saleData = {
          customerName: 'Manager Refund Test Customer',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        };

        const saleResponse = await ctx.request
          .post('/api/sales')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(saleData);

        // Try to refund as cashier (should fail)
        const refundResponse = await ctx.request
          .patch(`/api/sales/${saleResponse.body.id}/refund`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send({ reason: 'Test' });

        expectForbidden(refundResponse);
      });

      it('should fail without authentication', async () => {
        const response = await ctx.request
          .patch(`/api/sales/${refundTestSaleId}/refund`)
          .send({ reason: 'Test' });

        expectUnauthorized(response);
      });
    });

    describe('PATCH /api/sales/:id (Update Draft)', () => {
      let updateTestSaleId: string;

      beforeAll(async () => {
        // Create a draft sale for update tests
        const draftData = {
          customerName: 'Update Test Customer',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        };

        const response = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(draftData);

        updateTestSaleId = response.body.id;
      });

      it('should update draft sale', async () => {
        const updateData = {
          customerName: 'Updated Customer Name',
          customerPhone: '+62 812 3456 7890',
          notes: 'Updated notes',
          paymentMethod: 'TRANSFER',
          amountPaid: 8000,
        };

        const response = await ctx.request
          .patch(`/api/sales/${updateTestSaleId}`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(updateData);

        expectSuccess(response);
        expect(response.body.customerName).toBe('Updated Customer Name');
        expect(response.body.customerPhone).toBe('+62 812 3456 7890');
        expect(response.body.notes).toBe('Updated notes');
        expect(response.body.paymentMethod).toBe('TRANSFER');
        expect(response.body.amountPaid).toBe(8000);
      });

      it('should fail to update completed sale', async () => {
        const updateData = {
          customerName: 'Should Not Update',
        };

        const response = await ctx.request
          .patch(`/api/sales/${testSaleId}`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(updateData);

        expectConflict(response);
        expect(response.body.message).toContain('Hanya transaksi draft yang dapat diubah');
      });

      it('should update draft sale with new items and recalculate totals', async () => {
        const updateData = {
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 2,
              unitPrice: 8000,
            },
          ],
          customerName: 'Updated Customer Name',
          paymentMethod: 'TRANSFER',
          amountPaid: 20000,
        };

        const response = await ctx.request
          .patch(`/api/sales/${updateTestSaleId}`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(updateData);

        expectSuccess(response);
        expect(response.body.status).toBe('DRAFT');
        expect(response.body.customerName).toBe('Updated Customer Name');
        expect(response.body.paymentMethod).toBe('TRANSFER');
        expect(response.body.amountPaid).toBe(20000);
        expect(response.body.subtotal).toBe(16000); // 2 * 8000
        expect(response.body.totalAmount).toBe(16000);
        expect(response.body.changeAmount).toBe(4000); // 20000 - 16000
        expect(response.body.saleItems).toHaveLength(1);
        expect(response.body.saleItems[0].quantity).toBe(2);
        expect(response.body.saleItems[0].unitPrice).toBe(8000);
      });

      it('should fail without authentication', async () => {
        const response = await ctx.request
          .patch(`/api/sales/${updateTestSaleId}`)
          .send({ customerName: 'Test' });

        expectUnauthorized(response);
      });
    });

    // Enhanced updateDraft Tests
    describe('Enhanced updateDraft Functionality', () => {
      let enhancedTestSaleId: string;
      let testProductId2: string;
      let testUnitId2: string;

      beforeAll(async () => {
        // Create a second product for multi-item tests
        const baseUnit2 = await testSetup.createTestProductUnit(ctx.users.admin.id);
        const product2 = await testSetup.createTestProduct(baseUnit2.id, ctx.users.admin.id);
        await testSetup.createTestInventoryItem(product2.id, baseUnit2.id, testSupplierId, ctx.users.admin.id);
        testProductId2 = product2.id;
        testUnitId2 = baseUnit2.id;

        // Create a draft sale for enhanced update tests
        const draftData = {
          customerName: 'Enhanced Test Customer',
          paymentMethod: 'CASH',
          amountPaid: 15000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        };

        const response = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(draftData);

        enhancedTestSaleId = response.body.id;
      });

      it('should update items with discounts and recalculate totals', async () => {
        const updateData = {
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 3,
              unitPrice: 10000,
              discountType: 'PERCENTAGE',
              discountValue: 10,
              notes: 'Updated item with discount',
            },
          ],
          discountType: 'FIXED',
          discountValue: 2000,
          taxAmount: 1500,
          amountPaid: 30000,
        };

        const response = await ctx.request
          .patch(`/api/sales/${enhancedTestSaleId}`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(updateData);

        expectSuccess(response);

        // Item calculations: 3 * 10000 = 30000, 10% discount = 3000, item total = 27000
        // Sale discount: 2000
        // After discount: 27000 - 2000 = 25000
        // Tax: 1500
        // Total: 25000 + 1500 = 26500
        // Change: 30000 - 26500 = 3500

        expect(response.body.subtotal).toBe(27000);
        expect(response.body.discountAmount).toBe(2000);
        expect(response.body.taxAmount).toBe(1500);
        expect(response.body.totalAmount).toBe(26500);
        expect(response.body.changeAmount).toBe(3500);
        expect(response.body.saleItems[0].discountAmount).toBe(3000);
        expect(response.body.saleItems[0].totalPrice).toBe(27000);
      });

      it('should add multiple items and recalculate correctly', async () => {
        const updateData = {
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 2,
              unitPrice: 5000,
            },
            {
              productId: testProductId2,
              unitId: testUnitId2,
              quantity: 1,
              unitPrice: 12000,
              discountType: 'FIXED',
              discountValue: 1000,
            },
          ],
          amountPaid: 25000,
        };

        const response = await ctx.request
          .patch(`/api/sales/${enhancedTestSaleId}`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(updateData);

        expectSuccess(response);

        // Item 1: 2 * 5000 = 10000
        // Item 2: 1 * 12000 = 12000, discount 1000 = 11000
        // Subtotal: 10000 + 11000 = 21000

        expect(response.body.subtotal).toBe(21000);
        expect(response.body.totalAmount).toBe(21000);
        expect(response.body.changeAmount).toBe(4000);
        expect(response.body.saleItems).toHaveLength(2);
      });

      it('should remove all items and add new ones', async () => {
        const updateData = {
          items: [
            {
              productId: testProductId2,
              unitId: testUnitId2,
              quantity: 1,
              unitPrice: 15000,
            },
          ],
          customerName: 'Completely Updated Customer',
          amountPaid: 20000,
        };

        const response = await ctx.request
          .patch(`/api/sales/${enhancedTestSaleId}`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(updateData);

        expectSuccess(response);
        expect(response.body.customerName).toBe('Completely Updated Customer');
        expect(response.body.subtotal).toBe(15000);
        expect(response.body.totalAmount).toBe(15000);
        expect(response.body.changeAmount).toBe(5000);
        expect(response.body.saleItems).toHaveLength(1);
        expect(response.body.saleItems[0].productId).toBe(testProductId2);
      });

      it('should fail with empty items array', async () => {
        const updateData = {
          items: [],
        };

        const response = await ctx.request
          .patch(`/api/sales/${enhancedTestSaleId}`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(updateData);

        expect(response.status).toBe(400);
        expect(response.body.message).toContain('items must contain at least 1 elements');
      });

      it('should fail with non-existent product', async () => {
        const updateData = {
          items: [
            {
              productId: 'non-existent-product-id',
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 10000,
            },
          ],
        };

        const response = await ctx.request
          .patch(`/api/sales/${enhancedTestSaleId}`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(updateData);

        expectNotFound(response);
      });

      it('should handle insufficient stock with warning only (draft mode)', async () => {
        const updateData = {
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 999999, // Very large quantity to trigger insufficient stock
              unitPrice: 10000,
            },
          ],
        };

        const response = await ctx.request
          .patch(`/api/sales/${enhancedTestSaleId}`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(updateData);

        // Should succeed with warning (draft mode allows insufficient stock)
        expectSuccess(response);
        expect(response.body.saleItems[0].quantity).toBe(999999);
      });
    });
  });

  // Receipt Generation Tests
  describe('Receipt Generation', () => {
    let receiptTestSaleId: string;

    beforeAll(async () => {
      // Create a completed sale for receipt tests
      const saleData = {
        customerId: testCustomerId,
        customerName: 'Receipt Test Customer',
        customerPhone: '+62 813 1234 5678',
        paymentMethod: 'CASH',
        amountPaid: 20000,
        taxAmount: 1100,
        discountType: 'FIXED_AMOUNT',
        discountValue: 1000,
        notes: 'Special receipt test sale',
        items: [
          {
            productId: testProductId,
            unitId: testUnitId,
            quantity: 2,
            unitPrice: 7500,
            discountType: 'PERCENTAGE',
            discountValue: 5,
            notes: 'Item note for receipt',
          },
        ],
      };

      const response = await ctx.request
        .post('/api/sales')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(saleData);

      receiptTestSaleId = response.body.id;
    });

    describe('GET /api/sales/:id/receipt', () => {
      it('should generate receipt for completed sale', async () => {
        const response = await ctx.request
          .get(`/api/sales/${receiptTestSaleId}/receipt`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectSuccess(response);

        // Validate receipt structure
        expect(response.body).toHaveProperty('pharmacy');
        expect(response.body).toHaveProperty('transaction');
        expect(response.body).toHaveProperty('customer');
        expect(response.body).toHaveProperty('items');
        expect(response.body).toHaveProperty('totals');
        expect(response.body).toHaveProperty('payment');
        expect(response.body).toHaveProperty('footer');
        expect(response.body).toHaveProperty('formatting');

        // Validate pharmacy information
        expect(response.body.pharmacy.name).toBeDefined();
        expect(response.body.pharmacy.address).toBeDefined();
        expect(response.body.pharmacy.phone).toBeDefined();

        // Validate transaction information
        expect(response.body.transaction.saleNumber).toBeDefined();
        expect(response.body.transaction.date).toBeDefined();
        expect(response.body.transaction.time).toBeDefined();
        expect(response.body.transaction.cashier.name).toBeDefined();

        // Validate customer information
        expect(response.body.customer.name).toBe('Receipt Test Customer');
        expect(response.body.customer.phone).toBe('+62 813 1234 5678');
        expect(response.body.customer.type).toBe('REGISTERED');

        // Validate items
        expect(response.body.items).toBeInstanceOf(Array);
        expect(response.body.items.length).toBeGreaterThan(0);
        const item = response.body.items[0];
        expect(item.name).toBeDefined();
        expect(item.code).toBeDefined();
        expect(item.quantity).toBe(2);
        expect(item.unitPrice).toBe(7500);
        expect(item.subtotal).toBeDefined();

        // Validate totals
        expect(response.body.totals.subtotal).toBeDefined();
        expect(response.body.totals.discount).toBeDefined();
        expect(response.body.totals.total).toBeDefined();

        // Validate payment
        expect(response.body.payment.method).toBe('Tunai');
        expect(response.body.payment.amountPaid).toBe(20000);
        expect(response.body.payment.change).toBeDefined();

        // Validate formatting hints
        expect(response.body.formatting.paperWidth).toBe(58);
        expect(response.body.formatting.lineBreaks).toBeDefined();
        expect(response.body.formatting.alignment).toBeDefined();
        expect(response.body.formatting.fontSizes).toBeDefined();
      });

      it('should generate receipt for walk-in customer', async () => {
        // Create a walk-in customer sale
        const walkInSaleData = {
          customerName: 'Walk-in Receipt Customer',
          paymentMethod: 'TRANSFER',
          amountPaid: 7500,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        };

        const saleResponse = await ctx.request
          .post('/api/sales')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(walkInSaleData);

        const receiptResponse = await ctx.request
          .get(`/api/sales/${saleResponse.body.id}/receipt`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectSuccess(receiptResponse);
        expect(receiptResponse.body.customer.name).toBe('Walk-in Receipt Customer');
        expect(receiptResponse.body.customer.type).toBe('WALK_IN');
        expect(receiptResponse.body.payment.method).toBe('Transfer Bank');
      });

      it('should fail for non-existent sale', async () => {
        const response = await ctx.request
          .get('/api/sales/non-existent-id/receipt')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectNotFound(response);
      });

      it('should fail without authentication', async () => {
        const response = await ctx.request
          .get(`/api/sales/${receiptTestSaleId}/receipt`);

        expectUnauthorized(response);
      });
    });

    describe('GET /api/sales/:id/receipt/wide', () => {
      it('should generate wide receipt for 80mm thermal paper', async () => {
        const response = await ctx.request
          .get(`/api/sales/${receiptTestSaleId}/receipt/wide`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectSuccess(response);

        // Validate wide format specific properties
        expect(response.body.formatting.paperWidth).toBe(80);
        expect(response.body.formatting.fontSizes.header).toBe('large');
        expect(response.body.formatting.fontSizes.totals).toBe('large');

        // Should have same structure as regular receipt
        expect(response.body).toHaveProperty('pharmacy');
        expect(response.body).toHaveProperty('transaction');
        expect(response.body).toHaveProperty('customer');
        expect(response.body).toHaveProperty('items');
        expect(response.body).toHaveProperty('totals');
        expect(response.body).toHaveProperty('payment');
        expect(response.body).toHaveProperty('footer');
      });

      it('should fail for non-existent sale', async () => {
        const response = await ctx.request
          .get('/api/sales/non-existent-id/receipt/wide')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectNotFound(response);
      });

      it('should fail without authentication', async () => {
        const response = await ctx.request
          .get(`/api/sales/${receiptTestSaleId}/receipt/wide`);

        expectUnauthorized(response);
      });
    });

    describe('GET /api/sales/:id/receipt/validate', () => {
      it('should validate receipt generation for completed sale', async () => {
        const response = await ctx.request
          .get(`/api/sales/${receiptTestSaleId}/receipt/validate`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectSuccess(response);
        expect(response.body.canGenerate).toBe(true);
        expect(response.body.reason).toBeUndefined();
      });

      it('should fail validation for draft sale', async () => {
        // Create a draft sale
        const draftData = {
          customerName: 'Draft Receipt Customer',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        };

        const draftResponse = await ctx.request
          .post('/api/sales/draft')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(draftData);

        const validateResponse = await ctx.request
          .get(`/api/sales/${draftResponse.body.id}/receipt/validate`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectSuccess(validateResponse);
        expect(validateResponse.body.canGenerate).toBe(false);
        expect(validateResponse.body.reason).toContain('Tidak dapat membuat struk untuk transaksi draft');
      });

      it('should fail validation for cancelled sale', async () => {
        // Create and cancel a sale
        const saleData = {
          customerName: 'Cancel Receipt Customer',
          paymentMethod: 'CASH',
          amountPaid: 10000,
          items: [
            {
              productId: testProductId,
              unitId: testUnitId,
              quantity: 1,
              unitPrice: 7500,
            },
          ],
        };

        const saleResponse = await ctx.request
          .post('/api/sales')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send(saleData);

        await ctx.request
          .patch(`/api/sales/${saleResponse.body.id}/cancel`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
          .send({ reason: 'Test cancellation' });

        const validateResponse = await ctx.request
          .get(`/api/sales/${saleResponse.body.id}/receipt/validate`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectSuccess(validateResponse);
        expect(validateResponse.body.canGenerate).toBe(false);
        expect(validateResponse.body.reason).toContain('Tidak dapat membuat struk untuk transaksi yang dibatalkan');
      });

      it('should fail validation for non-existent sale', async () => {
        const response = await ctx.request
          .get('/api/sales/non-existent-id/receipt/validate')
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectSuccess(response);
        expect(response.body.canGenerate).toBe(false);
        expect(response.body.reason).toContain('Transaksi tidak ditemukan');
      });

      it('should fail without authentication', async () => {
        const response = await ctx.request
          .get(`/api/sales/${receiptTestSaleId}/receipt/validate`);

        expectUnauthorized(response);
      });
    });

    describe('Receipt Data Immutability', () => {
      it('should generate consistent receipt data for same sale', async () => {
        const response1 = await ctx.request
          .get(`/api/sales/${receiptTestSaleId}/receipt`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        // Wait a moment and generate again
        await new Promise(resolve => setTimeout(resolve, 100));

        const response2 = await ctx.request
          .get(`/api/sales/${receiptTestSaleId}/receipt`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectSuccess(response1);
        expectSuccess(response2);

        // Receipt data should be identical (immutable)
        expect(response1.body.transaction.saleNumber).toBe(response2.body.transaction.saleNumber);
        expect(response1.body.totals.total).toBe(response2.body.totals.total);
        expect(response1.body.payment.amountPaid).toBe(response2.body.payment.amountPaid);
        expect(response1.body.items.length).toBe(response2.body.items.length);
      });

      it('should include all required audit information', async () => {
        const response = await ctx.request
          .get(`/api/sales/${receiptTestSaleId}/receipt`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectSuccess(response);

        // Audit trail information
        expect(response.body.transaction.saleNumber).toBeDefined();
        expect(response.body.transaction.date).toBeDefined();
        expect(response.body.transaction.time).toBeDefined();
        expect(response.body.transaction.cashier.id).toBeDefined();
        expect(response.body.transaction.cashier.name).toBeDefined();

        // Financial information
        expect(response.body.totals.subtotal).toBeDefined();
        expect(response.body.totals.discount).toBeDefined();
        expect(response.body.totals.total).toBeDefined();
        expect(response.body.payment.method).toBeDefined();
        expect(response.body.payment.amountPaid).toBeDefined();
        expect(response.body.payment.change).toBeDefined();
      });
    });

    describe('Database-Driven Pharmacy Settings Integration', () => {
      it('should use pharmacy settings from database in receipt generation', async () => {
        // Update pharmacy settings in database
        const customPharmacySettings = {
          pharmacyName: 'Custom Receipt Test Pharmacy',
          pharmacyAddress: 'Custom Address for Receipt Test',
          pharmacyPhone: '+62 21 9999 8888',
          pharmacyLicense: 'CUSTOM.RECEIPT.LICENSE.789',
        };

        await ctx.request
          .put('/api/settings/pharmacy')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send(customPharmacySettings);

        // Generate receipt and verify it uses database settings
        const response = await ctx.request
          .get(`/api/sales/${receiptTestSaleId}/receipt`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectSuccess(response);

        // Verify pharmacy information comes from database settings
        expect(response.body.pharmacy.name).toBe(customPharmacySettings.pharmacyName);
        expect(response.body.pharmacy.address).toBe(customPharmacySettings.pharmacyAddress);
        expect(response.body.pharmacy.phone).toBe(customPharmacySettings.pharmacyPhone);
        expect(response.body.pharmacy.license).toBe(customPharmacySettings.pharmacyLicense);
      });

      it('should use fallback values when pharmacy settings are missing', async () => {
        // Clear specific pharmacy settings
        await ctx.prisma.appSettings.deleteMany({
          where: {
            settingKey: {
              in: ['pharmacyName', 'pharmacyAddress', 'pharmacyPhone']
            }
          }
        });

        // Generate receipt and verify it uses fallback values
        const response = await ctx.request
          .get(`/api/sales/${receiptTestSaleId}/receipt`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectSuccess(response);

        // Verify fallback values are used
        expect(response.body.pharmacy.name).toBe('Apotek Sehat Bersama'); // Default fallback
        expect(response.body.pharmacy.address).toBe('Jl. Kesehatan No. 123, Jakarta Selatan 12345'); // Default fallback
        expect(response.body.pharmacy.phone).toBe('(021) 1234-5678'); // Default fallback

        // Restore settings for other tests
        await testSetup.seedPharmacySettings();
      });

      it('should reflect pharmacy settings changes immediately in receipts', async () => {
        // Create initial settings
        await ctx.request
          .put('/api/settings/pharmacy')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({ pharmacyName: 'Initial Pharmacy Name' });

        // Generate receipt with initial settings
        const response1 = await ctx.request
          .get(`/api/sales/${receiptTestSaleId}/receipt`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectSuccess(response1);
        expect(response1.body.pharmacy.name).toBe('Initial Pharmacy Name');

        // Update settings
        await ctx.request
          .put('/api/settings/pharmacy')
          .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
          .send({ pharmacyName: 'Updated Pharmacy Name' });

        // Generate receipt with updated settings
        const response2 = await ctx.request
          .get(`/api/sales/${receiptTestSaleId}/receipt`)
          .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`);

        expectSuccess(response2);
        expect(response2.body.pharmacy.name).toBe('Updated Pharmacy Name');

        // Verify the change was reflected immediately
        expect(response1.body.pharmacy.name).not.toBe(response2.body.pharmacy.name);
      });
    });
  });
});
