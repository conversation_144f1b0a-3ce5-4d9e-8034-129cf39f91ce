import { IntegrationTestSetup, TestContext, expectSuccess, expectValidationError, expectNotFound } from './test-setup';
import { AllocationMethod } from '../../src/inventory/dto/stock-allocation.dto';
import { ProductType, ProductCategory, MedicineClassification, UnitType } from '@prisma/client';

describe('Stock Allocation Integration Tests', () => {
  let testSetup: IntegrationTestSetup;
  let ctx: TestContext;
  let testProductId: string;
  let testUnitId: string;
  let testSupplierId: string;
  let testInventoryItems: string[] = [];

  beforeAll(async () => {
    testSetup = new IntegrationTestSetup();
    ctx = await testSetup.setup();

    // Create test dependencies
    const testUnit = await ctx.prisma.productUnit.create({
      data: {
        name: 'Test Tablet',
        abbreviation: 'tab',
        type: UnitType.COUNT,
        isBaseUnit: true,
        description: 'Test tablet unit for allocation',
      },
    });
    testUnitId = testUnit.id;

    const testProduct = await ctx.prisma.product.create({
      data: {
        code: 'TEST-MED-001',
        name: 'Test Medicine',
        type: ProductType.MEDICINE,
        category: ProductCategory.ANALGESIC,
        medicineClassification: MedicineClassification.OBAT_BEBAS,
        baseUnitId: testUnitId,
        isActive: true,
        createdBy: ctx.users.admin.id,
      },
    });
    testProductId = testProduct.id;

    const testSupplier = await ctx.prisma.supplier.create({
      data: {
        code: 'TEST-SUP-001',
        name: 'Test Supplier',
        type: 'PBF',
        status: 'ACTIVE',
        address: 'Test Address',
        city: 'Jakarta',
        province: 'DKI Jakarta',
        postalCode: '12345',
        phone: '+62 21 1234 5678',
        email: '<EMAIL>',
        npwp: '12.345.678.9-012.345',
        createdBy: ctx.users.admin.id,
      },
    });
    testSupplierId = testSupplier.id;

    // Create multiple inventory batches for testing
    const now = new Date();
    const futureDate1 = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days
    const futureDate2 = new Date(now.getTime() + 60 * 24 * 60 * 60 * 1000); // 60 days
    const futureDate3 = new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000); // 90 days
    const pastDate1 = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
    const pastDate2 = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000); // 60 days ago

    // Batch 1: Oldest received, expires in 30 days (near expiry)
    const batch1 = await ctx.prisma.inventoryItem.create({
      data: {
        productId: testProductId,
        unitId: testUnitId,
        supplierId: testSupplierId,
        batchNumber: 'BATCH001',
        quantityOnHand: 100,
        costPrice: 5000,
        sellingPrice: 7500,
        location: 'Rak A-1',
        receivedDate: pastDate2,
        expiryDate: futureDate1,
        isActive: true,
        createdBy: ctx.users.admin.id,
      },
    });
    testInventoryItems.push(batch1.id);

    // Batch 2: Middle received, expires in 60 days
    const batch2 = await ctx.prisma.inventoryItem.create({
      data: {
        productId: testProductId,
        unitId: testUnitId,
        supplierId: testSupplierId,
        batchNumber: 'BATCH002',
        quantityOnHand: 150,
        costPrice: 5500,
        sellingPrice: 8000,
        location: 'Rak A-2',
        receivedDate: pastDate1,
        expiryDate: futureDate2,
        isActive: true,
        createdBy: ctx.users.admin.id,
      },
    });
    testInventoryItems.push(batch2.id);

    // Batch 3: Newest received, expires in 90 days
    const batch3 = await ctx.prisma.inventoryItem.create({
      data: {
        productId: testProductId,
        unitId: testUnitId,
        supplierId: testSupplierId,
        batchNumber: 'BATCH003',
        quantityOnHand: 200,
        costPrice: 6000,
        sellingPrice: 8500,
        location: 'Rak A-3',
        receivedDate: now,
        expiryDate: futureDate3,
        isActive: true,
        createdBy: ctx.users.admin.id,
      },
    });
    testInventoryItems.push(batch3.id);

    // Batch 4: Expired batch (should be skipped in FEFO)
    const expiredBatch = await ctx.prisma.inventoryItem.create({
      data: {
        productId: testProductId,
        unitId: testUnitId,
        supplierId: testSupplierId,
        batchNumber: 'EXPIRED001',
        quantityOnHand: 50,
        costPrice: 4000,
        sellingPrice: 6000,
        location: 'Rak B-1',
        receivedDate: new Date(now.getTime() - 120 * 24 * 60 * 60 * 1000), // 120 days ago
        expiryDate: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000), // Expired 10 days ago
        isActive: true,
        createdBy: ctx.users.admin.id,
      },
    });
    testInventoryItems.push(expiredBatch.id);

    console.log('Test setup completed:', {
      testProductId,
      testUnitId,
      testSupplierId,
      testInventoryItems,
    });
  });

  afterAll(async () => {
    await testSetup.teardown();
  });

  describe('POST /api/inventory/allocate', () => {
    it('should allocate stock using FEFO method', async () => {
      const allocationData = {
        productId: testProductId,
        requestedQuantity: 80,
        method: AllocationMethod.FEFO,
        allowPartialAllocation: true,
        reason: 'Test FEFO allocation',
        notes: 'Testing first expired first out logic',
      };

      const response = await ctx.request
        .post('/api/inventory/allocate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(allocationData);

      expectSuccess(response, 201);
      expect(response.body.success).toBe(true);
      expect(response.body.allocatedQuantity).toBe(80);
      expect(response.body.method).toBe(AllocationMethod.FEFO);
      expect(response.body.batches).toHaveLength(1);
      
      // Should allocate from BATCH001 (earliest expiry, non-expired)
      expect(response.body.batches[0].batchNumber).toBe('BATCH001');
      expect(response.body.batches[0].allocatedQuantity).toBe(80);
      expect(response.body.warnings.some((warning: string) => warning.includes('kedaluwarsa dalam'))).toBe(true);
    });

    it('should allocate stock using FIFO method', async () => {
      const allocationData = {
        productId: testProductId,
        requestedQuantity: 50,
        method: AllocationMethod.FIFO,
        allowPartialAllocation: true,
        reason: 'Test FIFO allocation',
        notes: 'Testing first in first out logic',
      };

      const response = await ctx.request
        .post('/api/inventory/allocate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(allocationData);

      expectSuccess(response, 201);
      expect(response.body.success).toBe(true);
      expect(response.body.allocatedQuantity).toBe(50);
      expect(response.body.method).toBe(AllocationMethod.FIFO);
      expect(response.body.batches).toHaveLength(2); // Will need 2 batches

      // Should allocate from remaining quantity in BATCH001 first (oldest received)
      expect(response.body.batches[0].batchNumber).toBe('BATCH001');
      expect(response.body.batches[0].allocatedQuantity).toBe(20); // Remaining from previous test
      expect(response.body.batches[1].batchNumber).toBe('BATCH002');
      expect(response.body.batches[1].allocatedQuantity).toBe(30); // Additional needed
    });

    it('should handle partial allocation across multiple batches', async () => {
      const allocationData = {
        productId: testProductId,
        requestedQuantity: 200,
        method: AllocationMethod.FEFO,
        allowPartialAllocation: true,
        reason: 'Test multi-batch allocation',
      };

      const response = await ctx.request
        .post('/api/inventory/allocate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(allocationData);

      expectSuccess(response, 201);
      expect(response.body.success).toBe(true);
      // After previous tests: BATCH001 has 0 left, BATCH002 has 150, BATCH003 has 200
      // So we should get 150 + 50 = 200 total (no shortfall)
      expect(response.body.allocatedQuantity).toBe(200);
      expect(response.body.batches.length).toBeGreaterThanOrEqual(1);
      expect(response.body.shortfall).toBeUndefined();
    });

    it('should fail allocation when insufficient stock and partial not allowed', async () => {
      const allocationData = {
        productId: testProductId,
        requestedQuantity: 500,
        method: AllocationMethod.FEFO,
        allowPartialAllocation: false,
        reason: 'Test insufficient stock',
      };

      const response = await ctx.request
        .post('/api/inventory/allocate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(allocationData);

      expectSuccess(response, 201);
      expect(response.body.success).toBe(false);
      // After previous tests, there should be some remaining stock but not 500
      expect(response.body.allocatedQuantity).toBe(0);
      expect(response.body.errors.some((error: string) => error.includes('Stok tidak mencukupi'))).toBe(true);
    });

    it('should require manager role for allocation', async () => {
      const allocationData = {
        productId: testProductId,
        requestedQuantity: 10,
        method: AllocationMethod.FIFO,
        reason: 'Test authorization',
      };

      const response = await ctx.request
        .post('/api/inventory/allocate')
        .set('Authorization', `Bearer ${ctx.users.cashier.accessToken}`)
        .send(allocationData);

      expect(response.status).toBe(403);
    });

    it('should validate allocation data', async () => {
      const invalidData = {
        productId: 'invalid-id',
        requestedQuantity: -10,
        method: 'INVALID_METHOD',
      };

      const response = await ctx.request
        .post('/api/inventory/allocate')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(invalidData);

      expectValidationError(response);
    });
  });

  describe('POST /api/inventory/allocate/preview', () => {
    it('should preview allocation without affecting stock', async () => {
      // First get current stock levels
      const beforeResponse = await ctx.request
        .get(`/api/inventory/products/${testProductId}/stock-summary`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      const beforeStock = beforeResponse.body.totalAvailable;

      if (beforeStock === 0) {
        // Skip this test if no stock is available
        console.log('Skipping preview test - no stock available');
        return;
      }

      const allocationData = {
        productId: testProductId,
        requestedQuantity: Math.min(10, beforeStock), // Request small amount
        method: AllocationMethod.FEFO,
        reason: 'Test preview allocation',
      };

      const response = await ctx.request
        .post('/api/inventory/allocate/preview')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`)
        .send(allocationData);

      expectSuccess(response, 201);
      expect(response.body.previewOnly).toBe(true);
      expect(response.body.allocatedQuantity).toBe(allocationData.requestedQuantity);

      // Verify stock levels haven't changed
      const afterResponse = await ctx.request
        .get(`/api/inventory/products/${testProductId}/stock-summary`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expect(afterResponse.body.totalAvailable).toBe(beforeStock);
    });
  });

  describe('GET /api/inventory/products/:productId/stock-summary', () => {
    it('should return comprehensive stock summary', async () => {
      const response = await ctx.request
        .get(`/api/inventory/products/${testProductId}/stock-summary`)
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body).toHaveProperty('totalAvailable');
      expect(response.body).toHaveProperty('batchCount');
      expect(response.body).toHaveProperty('nearExpiryCount');
      expect(response.body).toHaveProperty('expiredCount');
      expect(response.body).toHaveProperty('oldestBatch');
      expect(response.body).toHaveProperty('newestBatch');
      expect(response.body).toHaveProperty('earliestExpiry');

      // After all the allocations, stock might be depleted
      expect(response.body.totalAvailable).toBeGreaterThanOrEqual(0);
      expect(response.body.batchCount).toBeGreaterThanOrEqual(0);
      expect(response.body.expiredCount).toBe(1); // One expired batch (excluded from available)
      expect(response.body.nearExpiryCount).toBeGreaterThanOrEqual(0);
    });

    it('should return empty summary for non-existent product', async () => {
      const response = await ctx.request
        .get('/api/inventory/products/non-existent-id/stock-summary')
        .set('Authorization', `Bearer ${ctx.users.admin.accessToken}`);

      expectSuccess(response);
      expect(response.body.totalAvailable).toBe(0);
      expect(response.body.batchCount).toBe(0);
    });
  });
});
