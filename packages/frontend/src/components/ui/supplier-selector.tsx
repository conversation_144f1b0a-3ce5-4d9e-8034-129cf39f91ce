'use client';

import * as React from 'react';
import { Check, ChevronsUpDown, Search, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Supplier, SupplierQueryParams, SupplierStatus } from '@/types/supplier';
import { useSuppliers, useSupplier } from '@/hooks/useSuppliers';
import { useDebounce } from '@/hooks/useDebounce';

interface SupplierSelectorProps {
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  emptyMessage?: string;
  searchPlaceholder?: string;
  includeInactive?: boolean;
  allowNone?: boolean;
  noneLabel?: string;
}

export function SupplierSelector({
  value,
  onValueChange,
  placeholder = "Pilih supplier...",
  disabled = false,
  className,
  emptyMessage = "Tidak ada supplier ditemukan.",
  searchPlaceholder = "Cari supplier...",
  includeInactive = false,
  allowNone = false,
  noneLabel = "Tidak ada supplier",
}: SupplierSelectorProps) {
  const [open, setOpen] = React.useState(false);
  const [searchValue, setSearchValue] = React.useState('');
  const [page, setPage] = React.useState(1);
  const [allSuppliers, setAllSuppliers] = React.useState<Supplier[]>([]);

  // Debounce search to avoid too many API calls
  const debouncedSearch = useDebounce(searchValue, 300);

  // Query parameters for API call
  const queryParams: SupplierQueryParams = React.useMemo(() => ({
    page,
    limit: 20,
    search: debouncedSearch || undefined,
    status: includeInactive ? undefined : SupplierStatus.ACTIVE,
    sortBy: 'name',
    sortOrder: 'asc',
  }), [page, debouncedSearch, includeInactive]);

  // Fetch suppliers with search and pagination
  const { data: suppliersResponse, isLoading, error } = useSuppliers(queryParams);

  // Fetch specific supplier if we have a value that's not in the loaded suppliers
  const { data: specificSupplier } = useSupplier(value && value !== 'none' ? value : '');

  // Reset page when search changes
  React.useEffect(() => {
    setPage(1);
    // Don't clear allSuppliers here - let the API response handle it
  }, [debouncedSearch]);

  // Helper function to deduplicate suppliers by ID
  const deduplicateSuppliers = React.useCallback((suppliers: Supplier[]): Supplier[] => {
    const seen = new Set<string>();
    return suppliers.filter(supplier => {
      if (seen.has(supplier.id)) {
        return false;
      }
      seen.add(supplier.id);
      return true;
    });
  }, []);

  // Accumulate suppliers for infinite scroll
  React.useEffect(() => {
    if (suppliersResponse?.data) {
      if (page === 1) {
        // Reset suppliers for new search or initial load
        let newSuppliers = [...suppliersResponse.data];

        // If we have a specific supplier, add it to the beginning
        if (specificSupplier) {
          newSuppliers = [specificSupplier, ...newSuppliers];
        }

        // Deduplicate the entire list
        setAllSuppliers(deduplicateSuppliers(newSuppliers));
      } else {
        // Append suppliers for pagination with deduplication
        setAllSuppliers(prev => {
          const combined = [...prev, ...suppliersResponse.data];
          return deduplicateSuppliers(combined);
        });
      }
    }
  }, [suppliersResponse, page, deduplicateSuppliers]);

  // Handle specific supplier changes separately
  React.useEffect(() => {
    if (specificSupplier) {
      setAllSuppliers(prev => {
        // Check if specific supplier is already in the list
        const hasSpecificSupplier = prev.some(s => s.id === specificSupplier.id);

        if (!hasSpecificSupplier) {
          // Add specific supplier to the beginning and deduplicate
          return deduplicateSuppliers([specificSupplier, ...prev]);
        }

        return prev; // No change needed
      });
    }
  }, [specificSupplier, deduplicateSuppliers]);

  // Find the selected supplier
  const selectedSupplier = React.useMemo(() => {
    if (value === 'none') return null;

    // First try to find in loaded suppliers
    const foundInList = allSuppliers.find((supplier) => supplier.id === value);

    // If not found in list but we have a specific supplier, use that
    const finalSupplier = foundInList || specificSupplier;



    return finalSupplier || null;
  }, [allSuppliers, value, specificSupplier]);



  // Load more suppliers when scrolling to bottom
  const loadMore = React.useCallback(() => {
    if (suppliersResponse?.meta?.hasNextPage && !isLoading) {
      setPage(prev => prev + 1);
    }
  }, [suppliersResponse?.meta?.hasNextPage, isLoading]);

  const handleSelect = (supplierId: string) => {
    onValueChange(supplierId);
    setOpen(false);
    setSearchValue('');
    setPage(1);
  };

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) {
      setSearchValue('');
      setPage(1);
    }
  };

  const handleSearchChange = (newSearch: string) => {
    setSearchValue(newSearch);
  };

  const clearSearch = () => {
    setSearchValue('');
  };

  const displayValue = React.useMemo(() => {
    if (value === 'none') return noneLabel;
    if (selectedSupplier) return `${selectedSupplier.name} (${selectedSupplier.code})`;
    return placeholder;
  }, [value, selectedSupplier, noneLabel, placeholder]);

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "h-9 w-full justify-between text-left font-normal",
            (!selectedSupplier && value !== 'none') && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <span className="truncate">{displayValue}</span>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[--radix-popover-trigger-width] p-0" align="start">
        <Command shouldFilter={false}>
          <div className="flex items-center border-b px-3">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <input
              placeholder={searchPlaceholder}
              value={searchValue}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
              autoFocus
            />
            {searchValue && (
              <button
                onClick={clearSearch}
                className="ml-2 h-4 w-4 shrink-0 opacity-50 hover:opacity-100 transition-opacity"
                type="button"
              >
                ×
              </button>
            )}
          </div>
          <CommandList
            className="max-h-[300px] overflow-y-auto"
            onScroll={(e) => {
              const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
              if (scrollHeight - scrollTop <= clientHeight * 1.5) {
                loadMore();
              }
            }}
          >
            {isLoading && page === 1 ? (
              <div className="flex items-center justify-center py-6">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="ml-2 text-sm text-muted-foreground">Memuat supplier...</span>
              </div>
            ) : error ? (
              <div className="py-6 text-center text-sm text-muted-foreground">
                Gagal memuat supplier
              </div>
            ) : (
              <CommandGroup>
                {allowNone && (
                  <CommandItem
                    value="none"
                    onSelect={() => handleSelect('none')}
                    className="cursor-pointer py-2"
                  >
                    <div className="flex items-center justify-between w-full">
                      <span className="text-muted-foreground italic">{noneLabel}</span>
                      <Check
                        className={cn(
                          "ml-2 h-4 w-4 shrink-0",
                          value === 'none' ? "opacity-100" : "opacity-0"
                        )}
                      />
                    </div>
                  </CommandItem>
                )}

                {searchValue && allSuppliers.length > 0 && (
                  <div className="px-2 py-1.5 text-xs text-muted-foreground border-b">
                    {allSuppliers.length} supplier ditemukan
                    {suppliersResponse?.meta?.hasNextPage && " (scroll untuk lebih banyak)"}
                  </div>
                )}

                {allSuppliers.length === 0 && !isLoading ? (
                  <CommandEmpty>{emptyMessage}</CommandEmpty>
                ) : (
                  allSuppliers.map((supplier) => (
                    <CommandItem
                      key={supplier.id}
                      value={supplier.id}
                      onSelect={() => handleSelect(supplier.id)}
                      className="cursor-pointer py-2"
                    >
                      <div className="flex items-center justify-between w-full">
                        <div className="flex flex-col min-w-0 flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium truncate">{supplier.name}</span>
                            <span className="text-xs bg-muted px-1.5 py-0.5 rounded font-mono">
                              {supplier.code}
                            </span>
                          </div>
                          <div className="flex items-center gap-2 mt-0.5">
                            <span className="text-xs text-muted-foreground truncate">
                              {supplier.type}
                            </span>
                            {supplier.city && (
                              <span className="text-xs text-muted-foreground truncate">
                                • {supplier.city}
                              </span>
                            )}
                          </div>
                        </div>
                        <Check
                          className={cn(
                            "ml-2 h-4 w-4 shrink-0",
                            value === supplier.id ? "opacity-100" : "opacity-0"
                          )}
                        />
                      </div>
                    </CommandItem>
                  ))
                )}

                {isLoading && page > 1 && (
                  <div className="flex items-center justify-center py-2">
                    <Loader2 className="h-3 w-3 animate-spin" />
                    <span className="ml-2 text-xs text-muted-foreground">Memuat lebih banyak...</span>
                  </div>
                )}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
