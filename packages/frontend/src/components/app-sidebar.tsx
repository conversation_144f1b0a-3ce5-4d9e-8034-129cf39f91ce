"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { usePathname } from "next/navigation"
import Link from "next/link"
import {
  Home,
  Pill,
  Package,
  ShoppingCart,
  Users,
  Building2,
  DollarSign,
  Settings,
} from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarGroup,
  SidebarGroupContent,
} from "@/components/ui/sidebar"
import { NavUser } from "@/components/nav-user"

interface NavigationItem {
  name: string
  href: string
  icon: React.ComponentType<{ className?: string }>
}

const navigation: NavigationItem[] = [
  { name: 'Dashboard', href: '/dashboard', icon: Home },
  { name: 'Manajemen Produk', href: '/dashboard/products', icon: Pill },
  { name: 'Inventori', href: '/dashboard/inventory', icon: Package },
  { name: '<PERSON><PERSON><PERSON>', href: '/dashboard/sales', icon: ShoppingCart },
  { name: '<PERSON><PERSON><PERSON><PERSON>', href: '/dashboard/customers', icon: Users },
  { name: '<PERSON><PERSON>so<PERSON>', href: '/dashboard/suppliers', icon: Building2 },
  { name: 'Keuangan', href: '/dashboard/finance', icon: DollarSign },
  { name: 'Pengaturan', href: '/dashboard/settings', icon: Settings },
]

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  user?: any
  variant?: "sidebar" | "floating" | "inset"
}

export function AppSidebar({ user, variant = "sidebar", ...props }: AppSidebarProps) {
  const pathname = usePathname()
  const [pharmacyName, setPharmacyName] = useState('Apotek App')

  useEffect(() => {
    // Fetch pharmacy settings
    const fetchPharmacySettings = async () => {
      try {
        // This will be implemented when we have the API integration
        // For now, use default name
        setPharmacyName('Apotek Sehat Bersama')
      } catch (error) {
        console.error('Failed to fetch pharmacy settings:', error)
      }
    }

    fetchPharmacySettings()
  }, [])

  return (
    <Sidebar collapsible="offcanvas" variant={variant} {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 bg-primary rounded-lg flex items-center justify-center">
                    <Pill className="h-5 w-5 text-primary-foreground" />
                  </div>
                </div>
                <div className="ml-3">
                  <span className="text-base font-semibold">{pharmacyName}</span>
                </div>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigation.map((item) => {
                const isActive = pathname === item.href ||
                  (item.href !== '/dashboard' && pathname.startsWith(item.href))

                return (
                  <SidebarMenuItem key={item.name}>
                    <SidebarMenuButton
                      asChild
                      tooltip={item.name}
                      isActive={isActive}
                    >
                      <Link href={item.href}>
                        <item.icon className="h-5 w-5" />
                        <span>{item.name}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        {user && <NavUser user={user} />}
      </SidebarFooter>
    </Sidebar>
  )
}
