'use client';

import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown, MoreHorizontal, Eye, Edit, Trash2, UserX, UserCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { AlertDialogWrapper } from '@/components/alert-dialog-wrapper';
import { Product } from '@/types/product';
import {
  getProductTypeLabel,
  getProductTypeColor,
  getProductCategoryLabel,
  getProductCategoryColor,
  getMedicineClassificationLabel,
  getMedicineClassificationColor,
  getMedicineClassificationSymbol,
} from '@/lib/constants/product';
import { formatDate } from '@/lib/utils';

interface ColumnActionsProps {
  product: Product;
  onView: (product: Product) => void;
  onEdit: (product: Product) => void;
  onQuickView: (product: Product) => void;
  onActivate: (product: Product) => void;
  onDeactivate: (product: Product) => void;
  onHardDelete: (product: Product) => void;
  isActivating?: boolean;
  isDeactivating?: boolean;
  isHardDeleting?: boolean;
}

function ColumnActions({
  product,
  onView,
  onEdit,
  onQuickView,
  onActivate,
  onDeactivate,
  onHardDelete,
  isActivating,
  isDeactivating,
  isHardDeleting,
}: ColumnActionsProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Buka menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuLabel>Aksi</DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuItem onClick={() => onQuickView(product)}>
          <Eye className="mr-2 h-4 w-4" />
          Lihat Cepat
        </DropdownMenuItem>

        <DropdownMenuItem onClick={() => onView(product)}>
          <Eye className="mr-2 h-4 w-4" />
          Lihat Detail
        </DropdownMenuItem>

        <DropdownMenuItem onClick={() => onEdit(product)}>
          <Edit className="mr-2 h-4 w-4" />
          Edit
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        {/* Status-based actions */}
        {product.isActive && onDeactivate && (
          <AlertDialogWrapper
            variant="warning"
            title="Nonaktifkan Produk"
            description="Apakah Anda yakin ingin menonaktifkan produk ini?"
            confirmText="Nonaktifkan"
            cancelText="Batal"
            pendingText="Menonaktifkan..."
            disabled={isDeactivating}
            handler={() => onDeactivate(product)}
          >
            <DropdownMenuItem
              className="text-orange-600 focus:text-orange-600"
              onSelect={(event) => {
                event.preventDefault();
              }}
            >
              <UserX className="mr-2 h-4 w-4" />
              Nonaktifkan
            </DropdownMenuItem>
          </AlertDialogWrapper>
        )}

        {!product.isActive && onActivate && (
          <AlertDialogWrapper
            variant="primary"
            title="Aktifkan Produk"
            description="Apakah Anda yakin ingin mengaktifkan produk ini?"
            confirmText="Aktifkan"
            cancelText="Batal"
            pendingText="Mengaktifkan..."
            disabled={isActivating}
            handler={() => onActivate(product)}
          >
            <DropdownMenuItem
              className="text-green-600 focus:text-green-600"
              onSelect={(event) => {
                event.preventDefault();
              }}
            >
              <UserCheck className="mr-2 h-4 w-4" />
              Aktifkan
            </DropdownMenuItem>
          </AlertDialogWrapper>
        )}

        <DropdownMenuSeparator />

        {!product.isActive && onHardDelete && (
          <AlertDialogWrapper
            variant="destructive"
            title="Hapus Produk Permanen"
            description={`Anda akan menghapus permanen produk "${product.name}". Tindakan ini akan:

• Menghapus semua hierarki unit yang terkait secara permanen
• Menghilangkan semua catatan inventori yang terkait dengan produk ini
• Menyebabkan inkonsistensi pada riwayat transaksi yang mereferensikan produk ini
• Mempengaruhi akurasi pelaporan dan analisis data

Tindakan ini TIDAK DAPAT DIBATALKAN dan dapat berdampak serius pada integritas data sistem.`}
            confirmText="Hapus Permanen"
            cancelText="Batal"
            pendingText="Menghapus..."
            requireConfirmationText="HAPUS"
            confirmationPlaceholder="Ketik 'HAPUS' untuk mengonfirmasi"
            disabled={isHardDeleting}
            handler={() => onHardDelete(product)}
          >
            <DropdownMenuItem
              className="text-red-600 focus:text-red-600"
              onSelect={(event) => {
                event.preventDefault();
              }}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Hapus Permanen
            </DropdownMenuItem>
          </AlertDialogWrapper>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

interface LoadingStates {
  isActivating?: boolean;
  isDeactivating?: boolean;
  isHardDeleting?: boolean;
}

export const createProductColumns = (
  onView: (product: Product) => void,
  onEdit: (product: Product) => void,
  onQuickView: (product: Product) => void,
  onActivate: (product: Product) => void,
  onDeactivate: (product: Product) => void,
  onHardDelete: (product: Product) => void,
  loadingStates?: LoadingStates,
): ColumnDef<Product>[] => [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Pilih semua"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Pilih baris"
        />
      ),
      enableSorting: false,
      enableHiding: false,
      size: 40,
    },
    {
      accessorKey: 'code',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 px-2 lg:px-3"
          >
            Kode
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        return (
          <div className="font-mono text-sm font-medium">
            {row.getValue('code')}
          </div>
        );
      },
      size: 120,
    },
    {
      accessorKey: 'name',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 px-2 lg:px-3"
          >
            Nama Produk
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const product = row.original;
        return (
          <div className="min-w-[250px] max-w-[350px]">
            <div className="font-medium text-sm" title={row.getValue('name') as string}>
              {row.getValue('name')}
            </div>
            {product.genericName && (
              <div className="text-xs text-muted-foreground" title={product.genericName}>
                {product.genericName}
              </div>
            )}
            {product.strength && (
              <div className="text-xs text-blue-600" title={product.strength}>
                {product.strength}
              </div>
            )}
          </div>
        );
      },
      size: 350,
    },
    {
      accessorKey: 'type',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 px-2 lg:px-3"
          >
            Jenis
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const type = row.getValue('type') as Product['type'];
        return (
          <Badge variant="outline" className={`${getProductTypeColor(type)} text-xs`}>
            {getProductTypeLabel(type)}
          </Badge>
        );
      },
      size: 120,
    },
    {
      accessorKey: 'category',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 px-2 lg:px-3"
          >
            Kategori
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const category = row.getValue('category') as Product['category'];
        return (
          <Badge variant="outline" className={`${getProductCategoryColor(category)} text-xs`}>
            {getProductCategoryLabel(category)}
          </Badge>
        );
      },
      size: 150,
    },
    {
      accessorKey: 'medicineClassification',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 px-2 lg:px-3"
          >
            Klasifikasi
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const classification = row.getValue('medicineClassification') as Product['medicineClassification'];
        const symbol = getMedicineClassificationSymbol(classification);
        return (
          <div className="flex items-center gap-1">
            {symbol && <span className="text-sm">{symbol}</span>}
            <Badge variant="outline" className={`${getMedicineClassificationColor(classification)} text-xs`}>
              {getMedicineClassificationLabel(classification)}
            </Badge>
          </div>
        );
      },
      size: 150,
    },
    {
      accessorKey: 'manufacturer',
      header: 'Produsen',
      cell: ({ row }) => {
        const manufacturer = row.getValue('manufacturer') as string;
        return manufacturer ? (
          <div className="text-sm" title={manufacturer}>
            {manufacturer}
          </div>
        ) : (
          <span className="text-muted-foreground text-sm">-</span>
        );
      },
      size: 150,
    },
    {
      accessorKey: 'baseUnit',
      header: 'Unit Dasar',
      cell: ({ row }) => {
        const product = row.original;
        return (
          <div className="text-sm">
            {product.baseUnit.name}
          </div>
        );
      },
      size: 100,
    },
    {
      accessorKey: 'isActive',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 px-2 lg:px-3"
          >
            Status
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const isActive = row.getValue('isActive') as boolean;
        return (
          <Badge variant={isActive ? 'default' : 'secondary'} className="text-xs">
            {isActive ? 'Aktif' : 'Tidak Aktif'}
          </Badge>
        );
      },
      size: 100,
    },
    {
      accessorKey: 'createdAt',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 px-2 lg:px-3"
          >
            Dibuat
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        return (
          <div className="text-sm text-muted-foreground">
            {formatDate(row.getValue('createdAt'))}
          </div>
        );
      },
      size: 120,
    },
    {
      id: 'actions',
      header: () => (
        <div className="flex items-center justify-center gap-1">
          <span>Aksi</span>
          <div className="w-1 h-1 bg-blue-500 rounded-full animate-pulse" title="Kolom ini selalu terlihat"></div>
        </div>
      ),
      enableHiding: false,
      cell: ({ row }) => {
        const product = row.original;
        return (
          <div className="flex justify-center min-w-[80px]">
            <ColumnActions
              product={product}
              onView={onView}
              onEdit={onEdit}
              onQuickView={onQuickView}
              onActivate={onActivate}
              onDeactivate={onDeactivate}
              onHardDelete={onHardDelete}
              isActivating={loadingStates?.isActivating}
              isDeactivating={loadingStates?.isDeactivating}
              isHardDeleting={loadingStates?.isHardDeleting}
            />
          </div>
        );
      },
      size: 80,
    },
  ];
