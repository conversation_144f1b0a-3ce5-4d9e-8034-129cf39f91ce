'use client';

import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, Download, Upload, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { DataTable } from '@/components/products/data-table';
import { createProductColumns } from '@/components/products/columns';
import { ProductImportModal } from '@/components/products/ProductImportModal';
import { ProductExportModal } from '@/components/products/ProductExportModal';
import { ProductQuickView } from '@/components/products/product-quick-view';
import { Product, ProductQueryParams } from '@/types/product';
import {
  useProducts,
  useProductsInvalidate,
  useActivateProduct,
  useDeactivateProduct,
  useHardDeleteProduct,
} from '@/hooks/useProducts';
import {
  PRODUCT_TYPE_OPTIONS,
  PRODUCT_CATEGORY_OPTIONS,
  MEDICINE_CLASSIFICATION_OPTIONS,
  PRODUCT_STATUS_OPTIONS,
} from '@/lib/constants/product';

interface ProductsPageClientProps {
  initialQuery: ProductQueryParams;
}

export function ProductsPageClient({
  initialQuery,
}: ProductsPageClientProps) {
  const router = useRouter();

  // Query state - this will trigger the TanStack Query
  const [query, setQuery] = useState<ProductQueryParams>(initialQuery);

  // Use TanStack Query for data fetching
  const { data, isLoading, error, refetch } = useProducts(query);

  // Use invalidation hook for stats refresh
  const { invalidateStats } = useProductsInvalidate();

  // Use mutation hooks for product operations
  const activateProductMutation = useActivateProduct();
  const deactivateProductMutation = useDeactivateProduct();
  const hardDeleteProductMutation = useHardDeleteProduct();

  // Filter state
  const [filters, setFilters] = useState<ProductQueryParams>({
    type: initialQuery.type,
    category: initialQuery.category,
    medicineClassification: initialQuery.medicineClassification,
    manufacturer: initialQuery.manufacturer,
    isActive: initialQuery.isActive,
  });

  // Modal states
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [quickViewProduct, setQuickViewProduct] = useState<Product | null>(null);
  const [quickViewOpen, setQuickViewOpen] = useState(false);

  // Handlers
  const handleQueryChange = useCallback((newQuery: ProductQueryParams) => {
    setQuery(newQuery);

    // Update URL without navigation
    const searchParams = new URLSearchParams();
    Object.entries(newQuery).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.set(key, String(value));
      }
    });

    const newUrl = searchParams.toString()
      ? `${window.location.pathname}?${searchParams.toString()}`
      : window.location.pathname;

    window.history.replaceState({}, '', newUrl);
  }, []);

  const handleFilterChange = useCallback((key: keyof ProductQueryParams, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);

    // Apply filters to query
    handleQueryChange({
      ...query,
      ...newFilters,
      page: 1, // Reset to first page when filtering
    });
  }, [filters, query, handleQueryChange]);

  const clearFilters = useCallback(() => {
    const clearedFilters = {
      type: undefined,
      category: undefined,
      medicineClassification: undefined,
      manufacturer: undefined,
      isActive: undefined,
    };
    setFilters(clearedFilters);

    handleQueryChange({
      ...query,
      ...clearedFilters,
      page: 1,
    });
  }, [query, handleQueryChange]);

  // Navigation handlers
  const handleCreateProduct = useCallback(() => {
    router.push('/dashboard/products/create');
  }, [router]);

  const handleViewProduct = useCallback((product: Product) => {
    router.push(`/dashboard/products/${product.id}`);
  }, [router]);

  const handleEditProduct = useCallback((product: Product) => {
    router.push(`/dashboard/products/${product.id}/edit`);
  }, [router]);

  const handleQuickView = useCallback((product: Product) => {
    setQuickViewProduct(product);
    setQuickViewOpen(true);
  }, []);

  const handleQuickViewEdit = useCallback((product: Product) => {
    setQuickViewOpen(false);
    router.push(`/dashboard/products/${product.id}/edit`);
  }, [router]);

  const handleQuickViewFull = useCallback((product: Product) => {
    setQuickViewOpen(false);
    router.push(`/dashboard/products/${product.id}`);
  }, [router]);

  // Action handlers

  const handleActivateProduct = useCallback((product: Product) => {
    activateProductMutation.mutate(product.id);
  }, [activateProductMutation]);

  const handleDeactivateProduct = useCallback((product: Product) => {
    deactivateProductMutation.mutate(product.id);
  }, [deactivateProductMutation]);

  const handleHardDeleteProduct = useCallback((product: Product) => {
    hardDeleteProductMutation.mutate(product.id);
  }, [hardDeleteProductMutation]);

  const handleRefresh = useCallback(() => {
    refetch(); // TanStack Query refetch
    invalidateStats(); // Refresh stats
  }, [refetch, invalidateStats]);

  // Import/Export handlers
  const handleImportProducts = useCallback(() => {
    setIsImportModalOpen(true);
  }, []);

  const handleExportProducts = useCallback(() => {
    setIsExportModalOpen(true);
  }, []);

  const handleImportComplete = useCallback(() => {
    refetch(); // Refresh data after import
    invalidateStats(); // Refresh stats
  }, [refetch, invalidateStats]);

  const columns = createProductColumns(
    handleViewProduct,
    handleEditProduct,
    handleQuickView,
    handleActivateProduct,
    handleDeactivateProduct,
    handleHardDeleteProduct,
    {
      isActivating: activateProductMutation.isPending,
      isDeactivating: deactivateProductMutation.isPending,
      isHardDeleting: hardDeleteProductMutation.isPending,
    }
  );

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-4">Terjadi kesalahan saat memuat data produk</p>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Coba Lagi
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Manajemen Produk</h1>
          <p className="text-muted-foreground">
            Kelola produk obat, alat kesehatan, dan produk farmasi lainnya
          </p>
        </div>

        <div className="flex flex-col gap-2 sm:flex-row">
          <Button variant="outline" onClick={handleImportProducts} className="sm:order-1">
            <Upload className="mr-2 h-4 w-4" />
            Import
          </Button>
          <Button variant="outline" onClick={handleExportProducts} className="sm:order-2">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button variant="outline" onClick={handleRefresh} className="sm:order-3">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button onClick={handleCreateProduct} className="sm:order-4">
            <Plus className="mr-2 h-4 w-4" />
            Tambah Produk
          </Button>
        </div>
      </div>

      {/* Data Table */}
      <Card className="w-full min-w-0 max-w-full overflow-hidden">
        <CardContent className="p-0 sm:p-6">
          <div className="w-full min-w-0 max-w-full">
            <DataTable
              columns={columns}
              data={data?.data || []}
              meta={data?.meta || { total: 0, page: 1, limit: 10, totalPages: 0, hasNextPage: false, hasPreviousPage: false }}
              query={query}
              onQueryChange={handleQueryChange}
              loading={isLoading}
              searchPlaceholder="Cari produk..."
              onRowClick={handleViewProduct}
              filters={filters}
              onFilterChange={handleFilterChange}
              onClearFilters={clearFilters}
              filterOptions={{
                productTypes: PRODUCT_TYPE_OPTIONS,
                productCategories: PRODUCT_CATEGORY_OPTIONS,
                medicineClassifications: MEDICINE_CLASSIFICATION_OPTIONS,
                productStatuses: PRODUCT_STATUS_OPTIONS,
              }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Import Modal */}
      <ProductImportModal
        open={isImportModalOpen}
        onOpenChange={setIsImportModalOpen}
        onImportComplete={handleImportComplete}
      />

      {/* Export Modal */}
      <ProductExportModal
        open={isExportModalOpen}
        onOpenChange={setIsExportModalOpen}
        currentFilters={filters}
      />

      {/* Quick View Modal */}
      <ProductQuickView
        product={quickViewProduct}
        open={quickViewOpen}
        onOpenChange={setQuickViewOpen}
        onEdit={handleQuickViewEdit}
        onViewFull={handleQuickViewFull}
      />
    </div>
  );
}
