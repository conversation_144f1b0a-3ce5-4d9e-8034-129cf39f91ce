'use client';

import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Edit, ExternalLink, Package, Pill, FileText, Scale } from 'lucide-react';
import { Product } from '@/types/product';
import {
  getProductTypeLabel,
  getProductTypeColor,
  getProductCategoryLabel,
  getProductCategoryColor,
  getMedicineClassificationLabel,
  getMedicineClassificationColor,
  getMedicineClassificationSymbol,
} from '@/lib/constants/product';

interface ProductQuickViewProps {
  product: Product | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEdit?: (product: Product) => void;
  onViewFull?: (product: Product) => void;
}

export function ProductQuickView({
  product,
  open,
  onOpenChange,
  onEdit,
  onViewFull,
}: ProductQuickViewProps) {
  if (!product) return null;

  const classificationSymbol = getMedicineClassificationSymbol(product.medicineClassification);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[95vh] overflow-y-auto p-4 sm:p-6 lg:p-8 sm:max-w-3xl">
        <DialogHeader>
          <div className="space-y-4">
            <div className="flex-1 min-w-0">
              <DialogTitle className="text-xl sm:text-2xl font-bold break-words">
                {product.name}
              </DialogTitle>
              <DialogDescription className="text-sm sm:text-base mt-1">
                Kode: {product.code} • {getProductTypeLabel(product.type)}
              </DialogDescription>
            </div>
            <div className="flex flex-wrap items-center gap-2 shrink-0">
              <Badge variant={product.isActive ? 'default' : 'secondary'}>
                {product.isActive ? 'Aktif' : 'Tidak Aktif'}
              </Badge>
              <Badge variant="outline" className={getProductTypeColor(product.type)}>
                {getProductTypeLabel(product.type)}
              </Badge>
              <Badge variant="outline" className={getProductCategoryColor(product.category)}>
                {getProductCategoryLabel(product.category)}
              </Badge>
              {product.type === 'MEDICINE' && (
                <div className="flex items-center gap-1">
                  {classificationSymbol && <span className="text-sm">{classificationSymbol}</span>}
                  <Badge variant="outline" className={getMedicineClassificationColor(product.medicineClassification)}>
                    {getMedicineClassificationLabel(product.medicineClassification)}
                  </Badge>
                </div>
              )}
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-4 sm:space-y-6">
          {/* Basic Information */}
          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center gap-2 mb-4">
                <Package className="h-4 w-4 text-muted-foreground" />
                <h3 className="font-semibold text-sm sm:text-base">Informasi Dasar</h3>
              </div>
              <div className="grid gap-3 sm:gap-4 sm:grid-cols-2">
                <div>
                  <label className="text-xs sm:text-sm font-medium text-muted-foreground">Kode Produk</label>
                  <p className="text-sm sm:text-base font-mono font-medium">{product.code}</p>
                </div>
                <div>
                  <label className="text-xs sm:text-sm font-medium text-muted-foreground">Nama Produk</label>
                  <p className="text-sm sm:text-base font-medium">{product.name}</p>
                </div>
                {product.genericName && (
                  <div>
                    <label className="text-xs sm:text-sm font-medium text-muted-foreground">Nama Generik</label>
                    <p className="text-sm sm:text-base">{product.genericName}</p>
                  </div>
                )}
                {product.manufacturer && (
                  <div>
                    <label className="text-xs sm:text-sm font-medium text-muted-foreground">Produsen</label>
                    <p className="text-sm sm:text-base">{product.manufacturer}</p>
                  </div>
                )}
                {product.bpomNumber && (
                  <div>
                    <label className="text-xs sm:text-sm font-medium text-muted-foreground">Nomor BPOM</label>
                    <p className="text-sm sm:text-base font-mono">{product.bpomNumber}</p>
                  </div>
                )}
                <div>
                  <label className="text-xs sm:text-sm font-medium text-muted-foreground">Unit Dasar</label>
                  <p className="text-sm sm:text-base">
                    {product.baseUnit.name} ({product.baseUnit.abbreviation})
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Medicine Information - Only for medicines */}
          {product.type === 'MEDICINE' && (
            <Card>
              <CardContent className="p-4 sm:p-6">
                <div className="flex items-center gap-2 mb-4">
                  <Pill className="h-4 w-4 text-muted-foreground" />
                  <h3 className="font-semibold text-sm sm:text-base">Informasi Obat</h3>
                </div>
                <div className="grid gap-3 sm:gap-4 sm:grid-cols-2">
                  {product.activeIngredient && (
                    <div>
                      <label className="text-xs sm:text-sm font-medium text-muted-foreground">Kandungan Aktif</label>
                      <p className="text-sm sm:text-base">{product.activeIngredient}</p>
                    </div>
                  )}
                  {product.strength && (
                    <div>
                      <label className="text-xs sm:text-sm font-medium text-muted-foreground">Kekuatan</label>
                      <p className="text-sm sm:text-base">{product.strength}</p>
                    </div>
                  )}
                  {product.dosageForm && (
                    <div>
                      <label className="text-xs sm:text-sm font-medium text-muted-foreground">Bentuk Sediaan</label>
                      <p className="text-sm sm:text-base">{product.dosageForm}</p>
                    </div>
                  )}
                  {product.indication && (
                    <div>
                      <label className="text-xs sm:text-sm font-medium text-muted-foreground">Indikasi</label>
                      <p className="text-sm sm:text-base">{product.indication}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Stock Information */}
          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center gap-2 mb-4">
                <Scale className="h-4 w-4 text-muted-foreground" />
                <h3 className="font-semibold text-sm sm:text-base">Informasi Stok</h3>
              </div>
              <div className="grid gap-3 sm:gap-4 sm:grid-cols-3">
                {product.minimumStock !== null && product.minimumStock !== undefined && (
                  <div>
                    <label className="text-xs sm:text-sm font-medium text-muted-foreground">Stok Minimum</label>
                    <p className="text-sm sm:text-base">{product.minimumStock}</p>
                  </div>
                )}
                {product.maximumStock !== null && product.maximumStock !== undefined && (
                  <div>
                    <label className="text-xs sm:text-sm font-medium text-muted-foreground">Stok Maksimum</label>
                    <p className="text-sm sm:text-base">{product.maximumStock}</p>
                  </div>
                )}
                {product.reorderPoint !== null && product.reorderPoint !== undefined && (
                  <div>
                    <label className="text-xs sm:text-sm font-medium text-muted-foreground">Titik Reorder</label>
                    <p className="text-sm sm:text-base">{product.reorderPoint}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Description */}
          {product.description && (
            <Card>
              <CardContent className="p-4 sm:p-6">
                <div className="flex items-center gap-2 mb-4">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <h3 className="font-semibold text-sm sm:text-base">Deskripsi</h3>
                </div>
                <p className="text-sm sm:text-base text-muted-foreground">{product.description}</p>
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-end gap-2 sm:gap-3 pt-4 sm:pt-6 border-t">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="order-3 sm:order-1"
            >
              Tutup
            </Button>
            {onEdit && (
              <Button
                variant="outline"
                onClick={() => onEdit(product)}
                className="order-2 sm:order-2"
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
            )}
            {onViewFull && (
              <Button
                onClick={() => onViewFull(product)}
                className="order-1 sm:order-3"
              >
                <ExternalLink className="mr-2 h-4 w-4" />
                Lihat Detail Lengkap
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
