'use client';

import { ColumnDef } from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Eye, Edit, Trash2, Receipt, RefreshCw, X, Undo, ArrowUpDown, Printer } from 'lucide-react';
import { Sale, SaleStatus } from '@/types/sales';
import { AlertDialogWrapper } from '@/components/alert-dialog-wrapper';
import {
  formatCurrency,
  formatDate,
  formatSaleNumber,
  formatCustomerName,
  formatCashierName,
  getSaleStatusColor,
  getPaymentMethodColor,
  getSaleStatusLabel,
  getPaymentMethodLabel,
  isEditableSale,
  isCancellableSale,
  isRefundableSale,
  isCompletableSale,
  isDeletableSale
} from '@/lib/constants/sales';

interface CreateSaleColumnsProps {
  onView?: (sale: Sale) => void;
  onEdit?: (sale: Sale) => void;
  onDelete?: (sale: Sale) => void;
  onComplete?: (sale: Sale) => void;
  onCancel?: (sale: Sale) => void;
  onRefund?: (sale: Sale) => void;
  onGenerateReceipt?: (sale: Sale) => void;
  onGenerateReceiptWide?: (sale: Sale) => void;
}

interface LoadingStates {
  isCompleting?: boolean;
  isCancelling?: boolean;
  isRefunding?: boolean;
  isDeleting?: boolean;
  isGeneratingReceipt?: boolean;
  isGeneratingReceiptWide?: boolean;
}

export function createSaleColumns(
  actions: CreateSaleColumnsProps,
  loadingStates?: LoadingStates
): ColumnDef<Sale>[] {
  const {
    onView,
    onEdit,
    onDelete,
    onComplete,
    onCancel,
    onRefund,
    onGenerateReceipt,
    onGenerateReceiptWide,
  } = actions;
  return [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Pilih semua"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Pilih baris"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: 'saleNumber',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-semibold hover:bg-transparent"
          >
            No. Transaksi
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const sale = row.original;
        return (
          <div className="space-y-1">
            <div className="font-medium text-sm">{formatSaleNumber(sale.saleNumber)}</div>
            <div className="text-xs text-muted-foreground">
              {sale.saleItems?.length || 0} item
            </div>
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: 'saleDate',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-semibold hover:bg-transparent"
          >
            Tanggal
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const sale = row.original;
        return (
          <div className="text-sm">
            {formatDate(sale.saleDate)}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: 'customerName',
      header: 'Pelanggan',
      cell: ({ row }) => {
        const sale = row.original;
        const customerName = formatCustomerName(sale);
        const customerPhone = sale.customer?.phoneNumber || sale.customerPhone;

        return (
          <div className="space-y-1">
            <div className="font-medium text-sm">{customerName}</div>
            {customerPhone && (
              <div className="text-xs text-muted-foreground">{customerPhone}</div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'cashier',
      header: 'Kasir',
      cell: ({ row }) => {
        const sale = row.original;
        const cashierName = formatCashierName(sale.cashier);

        return (
          <div className="space-y-1">
            <div className="font-medium text-sm">{cashierName}</div>
            <div className="text-xs text-muted-foreground">{sale.cashier.email}</div>
          </div>
        );
      },
    },
    {
      accessorKey: 'totalAmount',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-semibold hover:bg-transparent"
          >
            Total
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const sale = row.original;
        return (
          <div className="text-right">
            <div className="font-medium">{formatCurrency(sale.totalAmount)}</div>
            {sale.discountAmount > 0 && (
              <div className="text-xs text-muted-foreground">
                Diskon: {formatCurrency(sale.discountAmount)}
              </div>
            )}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: 'paymentMethod',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-semibold hover:bg-transparent"
          >
            Metode Bayar
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const sale = row.original;
        return (
          <Badge variant="outline" className={getPaymentMethodColor(sale.paymentMethod)}>
            {getPaymentMethodLabel(sale.paymentMethod)}
          </Badge>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: 'status',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-semibold hover:bg-transparent"
          >
            Status
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const sale = row.original;
        return (
          <Badge variant="outline" className={getSaleStatusColor(sale.status)}>
            {getSaleStatusLabel(sale.status)}
          </Badge>
        );
      },
      enableSorting: true,
    },
    {
      id: 'actions',
      header: () => (
        <div className="flex items-center justify-center gap-1">
          <span>Aksi</span>
          <div className="w-1 h-1 bg-blue-500 rounded-full animate-pulse" title="Kolom ini selalu terlihat"></div>
        </div>
      ),
      enableHiding: false,
      cell: ({ row }) => {
        const sale = row.original;

        return (
          <div className="flex justify-center min-w-[80px]">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Buka menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuLabel>Aksi</DropdownMenuLabel>

                {onView && (
                  <DropdownMenuItem onClick={() => onView(sale)}>
                    <Eye className="mr-2 h-4 w-4" />
                    Lihat Detail
                  </DropdownMenuItem>
                )}

                {onEdit && isEditableSale(sale.status) && (
                  <DropdownMenuItem onClick={() => onEdit(sale)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                )}

                {onGenerateReceipt && sale.status === SaleStatus.COMPLETED && (
                  <DropdownMenuItem
                    onClick={() => onGenerateReceipt(sale)}
                    disabled={loadingStates?.isGeneratingReceipt}
                  >
                    <Receipt className="mr-2 h-4 w-4" />
                    Cetak Struk
                  </DropdownMenuItem>
                )}

                {onGenerateReceiptWide && sale.status === SaleStatus.COMPLETED && (
                  <DropdownMenuItem
                    onClick={() => onGenerateReceiptWide(sale)}
                    disabled={loadingStates?.isGeneratingReceiptWide}
                  >
                    <Printer className="mr-2 h-4 w-4" />
                    Cetak Struk Lebar
                  </DropdownMenuItem>
                )}

                <DropdownMenuSeparator />

                {onComplete && isCompletableSale(sale.status) && (
                  <AlertDialogWrapper
                    variant="primary"
                    title="Selesaikan Transaksi"
                    description={`Apakah Anda yakin ingin menyelesaikan transaksi ${sale.saleNumber}?`}
                    confirmText="Selesaikan"
                    cancelText="Batal"
                    pendingText="Menyelesaikan..."
                    disabled={loadingStates?.isCompleting}
                    handler={() => onComplete(sale)}
                  >
                    <DropdownMenuItem
                      className="text-green-600 focus:text-green-600"
                      onSelect={(event) => {
                        event.preventDefault();
                      }}
                    >
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Selesaikan
                    </DropdownMenuItem>
                  </AlertDialogWrapper>
                )}

                {onCancel && isCancellableSale(sale.status) && (
                  <AlertDialogWrapper
                    variant="warning"
                    title="Batalkan Transaksi"
                    description={`Apakah Anda yakin ingin membatalkan transaksi ${sale.saleNumber}?`}
                    confirmText="Batalkan"
                    cancelText="Batal"
                    pendingText="Membatalkan..."
                    disabled={loadingStates?.isCancelling}
                    handler={() => onCancel(sale)}
                  >
                    <DropdownMenuItem
                      className="text-orange-600 focus:text-orange-600"
                      onSelect={(event) => {
                        event.preventDefault();
                      }}
                    >
                      <X className="mr-2 h-4 w-4" />
                      Batalkan
                    </DropdownMenuItem>
                  </AlertDialogWrapper>
                )}

                {onRefund && isRefundableSale(sale.status) && (
                  <AlertDialogWrapper
                    variant="warning"
                    title="Refund Transaksi"
                    description={`Apakah Anda yakin ingin melakukan refund untuk transaksi ${sale.saleNumber}?`}
                    confirmText="Refund"
                    cancelText="Batal"
                    pendingText="Memproses refund..."
                    disabled={loadingStates?.isRefunding}
                    handler={() => onRefund(sale)}
                  >
                    <DropdownMenuItem
                      className="text-orange-600 focus:text-orange-600"
                      onSelect={(event) => {
                        event.preventDefault();
                      }}
                    >
                      <Undo className="mr-2 h-4 w-4" />
                      Refund
                    </DropdownMenuItem>
                  </AlertDialogWrapper>
                )}

                {onDelete && isDeletableSale(sale.status) && (
                  <>
                    <DropdownMenuSeparator />
                    <AlertDialogWrapper
                      variant="destructive"
                      title="Hapus Transaksi"
                      description={`Apakah Anda yakin ingin menghapus transaksi ${sale.saleNumber}? Tindakan ini tidak dapat dibatalkan.`}
                      confirmText="Hapus"
                      cancelText="Batal"
                      pendingText="Menghapus..."
                      disabled={loadingStates?.isDeleting}
                      handler={() => onDelete(sale)}
                    >
                      <DropdownMenuItem
                        className="text-red-600 focus:text-red-600"
                        onSelect={(event) => {
                          event.preventDefault();
                        }}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Hapus
                      </DropdownMenuItem>
                    </AlertDialogWrapper>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
      size: 80,
    },
  ];
}
