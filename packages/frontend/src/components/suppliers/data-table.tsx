'use client';

import * as React from 'react';
import {
  ColumnDef,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { ChevronDown, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { SupplierQueryParams, SupplierListResponse } from '@/types/supplier';

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  meta: SupplierListResponse['meta'];
  searchPlaceholder?: string;
  onRowClick?: (row: TData) => void;
  onQueryChange?: (query: SupplierQueryParams) => void;
  loading?: boolean;
  query: SupplierQueryParams;
  // Filter-related props
  filters: SupplierQueryParams;
  onFilterChange: (key: keyof SupplierQueryParams, value: any) => void;
  onClearFilters: () => void;
  filterOptions: {
    supplierTypes: { value: string; label: string }[];
    supplierStatuses: { value: string; label: string }[];
    provinces: string[];
  };
}

export function DataTable<TData, TValue>({
  columns,
  data,
  meta,
  searchPlaceholder = 'Cari...',
  onRowClick,
  onQueryChange,
  loading = false,
  query,
  filters,
  onFilterChange,
  onClearFilters,
  filterOptions,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [searchValue, setSearchValue] = React.useState(query.search || '');

  // Debounced search
  const [debouncedSearch, setDebouncedSearch] = React.useState(query.search || '');

  // Sync search value with query changes (when query comes from URL or external source)
  React.useEffect(() => {
    if (query.search !== searchValue) {
      setSearchValue(query.search || '');
      setDebouncedSearch(query.search || '');
    }
  }, [query.search]);

  // Sync sorting state with query
  React.useEffect(() => {
    if (query.sortBy && query.sortOrder) {
      setSorting([{
        id: query.sortBy,
        desc: query.sortOrder === 'desc'
      }]);
    } else {
      setSorting([]);
    }
  }, [query.sortBy, query.sortOrder]);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchValue);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchValue]);

  // Use refs to avoid stale closures
  const queryRef = React.useRef(query);
  const onQueryChangeRef = React.useRef(onQueryChange);

  React.useEffect(() => {
    queryRef.current = query;
    onQueryChangeRef.current = onQueryChange;
  });

  // Trigger query change when debounced search changes
  React.useEffect(() => {
    if (onQueryChangeRef.current && debouncedSearch !== queryRef.current.search) {
      onQueryChangeRef.current({
        ...queryRef.current,
        search: debouncedSearch || undefined,
        page: 1, // Reset to first page when searching
      });
    }
  }, [debouncedSearch]);

  // Handle sorting changes
  React.useEffect(() => {
    if (sorting.length > 0 && onQueryChangeRef.current) {
      const sort = sorting[0];
      const newSortBy = sort.id;
      const newSortOrder = sort.desc ? 'desc' : 'asc';

      // Only trigger if sort actually changed
      if (queryRef.current.sortBy !== newSortBy || queryRef.current.sortOrder !== newSortOrder) {
        onQueryChangeRef.current({
          ...queryRef.current,
          sortBy: newSortBy,
          sortOrder: newSortOrder,
          page: 1, // Reset to first page when sorting
        });
      }
    }
  }, [sorting]);

  // Handle scroll shadows and arrows for visual indication
  React.useEffect(() => {
    const scrollContainer = document.getElementById('table-scroll-container');
    const leftShadow = document.getElementById('left-scroll-shadow');
    const rightShadow = document.getElementById('right-scroll-shadow');
    const leftArrow = document.getElementById('left-scroll-arrow');

    if (!scrollContainer || !leftShadow || !rightShadow) return;

    const handleScroll = () => {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainer;
      const maxScroll = scrollWidth - clientWidth;

      // Show left shadow and arrow when scrolled right
      const showLeft = scrollLeft > 10;
      leftShadow.style.opacity = showLeft ? '1' : '0';
      if (leftArrow) leftArrow.style.opacity = showLeft ? '1' : '0';

      // Show right shadow when not fully scrolled right
      const showRight = scrollLeft < maxScroll - 10;
      rightShadow.style.opacity = showRight ? '1' : '0';
    };

    // Initial check
    handleScroll();

    scrollContainer.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', handleScroll);

    return () => {
      scrollContainer.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleScroll);
    };
  }, []);

  // Set responsive column visibility
  React.useEffect(() => {
    const handleResize = () => {
      const isMobile = window.innerWidth < 768;
      const isTablet = window.innerWidth < 1024;

      if (isMobile) {
        // On mobile, show only essential columns
        setColumnVisibility({
          select: false,
          phone: false,
          createdAt: false,
          city: false,
        });
      } else if (isTablet) {
        // On tablet, show more columns but hide some
        setColumnVisibility({
          phone: false,
          createdAt: false,
        });
      } else {
        // On desktop, show all columns
        setColumnVisibility({});
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnVisibility,
    },
    manualSorting: true,
    manualFiltering: true,
    manualPagination: true,
  });

  return (
    <div className="w-full min-w-0 max-w-full space-y-4">
      {/* Unified Single-Row Toolbar */}
      <div className="flex flex-col gap-3 lg:gap-4">
        {/* Main toolbar row - all controls in one line on desktop */}
        <div className="flex flex-col gap-3 lg:flex-row lg:items-center lg:gap-4">
          {/* Search Input */}
          <div className="relative flex-1 max-w-sm">
            <Input
              placeholder={searchPlaceholder}
              value={searchValue}
              onChange={(event) => setSearchValue(event.target.value)}
              className="pr-10 h-9"
              disabled={loading}
            />
            {loading && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}
          </div>

          {/* Filter Controls - responsive grid */}
          <div className="ml-auto flex flex-col gap-3 sm:flex-row sm:flex-wrap lg:flex-nowrap lg:gap-3">
            {/* Supplier Type Filter */}
            <div className="min-w-0 flex-shrink-0">
              <Select
                value={filters.type || 'all'}
                onValueChange={(value) => onFilterChange('type', value === 'all' ? undefined : value)}
                disabled={loading}
              >
                <SelectTrigger className="h-9 w-full sm:w-[140px]">
                  <SelectValue placeholder="Semua Jenis" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Jenis</SelectItem>
                  {filterOptions.supplierTypes.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Status Filter */}
            <div className="min-w-0 flex-shrink-0">
              <Select
                value={filters.status || 'all'}
                onValueChange={(value) => onFilterChange('status', value === 'all' ? undefined : value)}
                disabled={loading}
              >
                <SelectTrigger className="h-9 w-full sm:w-[130px]">
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  {filterOptions.supplierStatuses.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Province Filter */}
            <div className="min-w-0 flex-shrink-0">
              <Select
                value={filters.province || 'all'}
                onValueChange={(value) => onFilterChange('province', value === 'all' ? undefined : value)}
                disabled={loading}
              >
                <SelectTrigger className="h-9 w-full sm:w-[140px]">
                  <SelectValue placeholder="Semua Provinsi" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Provinsi</SelectItem>
                  {filterOptions.provinces.map((province) => (
                    <SelectItem key={province} value={province}>
                      {province}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Reset Button and Active Filter Badge */}
            <div className="flex items-center gap-2 flex-shrink-0">
              <Button
                variant="outline"
                size="sm"
                onClick={onClearFilters}
                disabled={loading}
                className="h-9"
              >
                Reset
              </Button>
              {(filters.search || filters.type || filters.status || filters.province) && (
                <Badge variant="secondary" className="text-xs whitespace-nowrap">
                  Filter aktif
                </Badge>
              )}
            </div>
          </div>

          {/* Column Visibility Control */}
          <div className="flex-shrink-0">
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="h-9">
                  Kolom <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {table
                  .getAllColumns()
                  .filter((column) => column.getCanHide())
                  .map((column) => {
                    return (
                      <DropdownMenuCheckboxItem
                        key={column.id}
                        className="capitalize"
                        checked={column.getIsVisible()}
                        onCheckedChange={(value) =>
                          column.toggleVisibility(!!value)
                        }
                      >
                        {column.id}
                      </DropdownMenuCheckboxItem>
                    );
                  })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Table Container with horizontal scroll and sticky actions */}
      <div className="w-full min-w-0 max-w-full overflow-hidden rounded-md border">
        {/* Enhanced Scroll indicators */}
        <div className="bg-blue-50 dark:bg-blue-950/30 px-4 py-3 text-sm text-blue-700 dark:text-blue-300 border-b border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-center gap-2">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="font-medium">Tabel dapat digulir horizontal</span>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            </div>
          </div>
          <div className="text-xs text-center mt-1 text-blue-600 dark:text-blue-400">
            ← Geser ke kanan untuk melihat semua kolom | Aksi selalu terlihat di kanan →
          </div>
        </div>

        {/* Table scroll container with visual scroll indicators */}
        <div className="relative">
          {/* Left scroll shadow */}
          <div className="absolute left-0 top-0 bottom-0 w-6 bg-gradient-to-r from-background to-transparent z-20 pointer-events-none opacity-0 transition-opacity duration-200" id="left-scroll-shadow"></div>

          {/* Right scroll shadow with arrow indicator */}
          <div className="absolute right-0 top-0 bottom-0 w-6 bg-gradient-to-l from-background to-transparent z-20 pointer-events-none opacity-100 transition-opacity duration-200" id="right-scroll-shadow">
            <div className="absolute right-1 top-1/2 transform -translate-y-1/2">
              <div className="text-blue-500 animate-bounce">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>
          </div>

          {/* Left scroll arrow indicator */}
          <div className="absolute left-0 top-1/2 transform -translate-y-1/2 z-20 pointer-events-none opacity-0 transition-opacity duration-200" id="left-scroll-arrow">
            <div className="text-blue-500 animate-bounce ml-1">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </div>
          </div>

          <div className="w-full overflow-x-auto" id="table-scroll-container">
            <Table className="min-w-[1200px]">
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      const isActionsColumn = header.id === 'actions';
                      return (
                        <TableHead
                          key={header.id}
                          className={`px-2 lg:px-4 ${isActionsColumn
                            ? 'sticky right-0 bg-background border-l shadow-lg z-10'
                            : ''
                            }`}
                        >
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                        </TableHead>
                      );
                    })}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {loading ? (
                  // Loading state
                  Array.from({ length: query.limit || 10 }).map((_, index) => (
                    <TableRow key={`loading-${index}`}>
                      {columns.map((_, colIndex) => (
                        <TableCell key={colIndex} className="px-2 lg:px-4">
                          <div className="h-4 bg-muted animate-pulse rounded"></div>
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      className={onRowClick ? 'cursor-pointer hover:bg-muted/50' : ''}
                      onClick={() => onRowClick?.(row.original)}
                    >
                      {row.getVisibleCells().map((cell) => {
                        const isActionsColumn = cell.column.id === 'actions';
                        return (
                          <TableCell
                            key={cell.id}
                            onClick={(e) => e.stopPropagation()}
                            className={`px-2 lg:px-4 ${isActionsColumn
                              ? 'sticky right-0 bg-background border-l shadow-lg z-10'
                              : ''
                              }`}
                          >
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext()
                            )}
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      <div className="flex flex-col items-center gap-2">
                        <div className="w-12 h-12 text-muted-foreground">
                          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                        <p className="text-muted-foreground">Tidak ada data ditemukan</p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between p-4">
          <div className="flex items-center gap-4">
            <div className="text-sm text-muted-foreground">
              Menampilkan {((query.page || 1) - 1) * (query.limit || 10) + 1} - {Math.min((query.page || 1) * (query.limit || 10), meta.total)} dari {meta.total} data
            </div>
            <Select
              value={String(query.limit || 10)}
              onValueChange={(value) => onQueryChange?.({
                ...query,
                limit: Number(value),
                page: 1,
              })}
              disabled={loading}
            >
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onQueryChange?.({
                ...query,
                page: 1,
              })}
              disabled={loading || !meta.hasPreviousPage}
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onQueryChange?.({
                ...query,
                page: (query.page || 1) - 1,
              })}
              disabled={loading || !meta.hasPreviousPage}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <div className="flex items-center gap-1">
              <span className="text-sm">Halaman</span>
              <span className="text-sm font-medium">{query.page || 1}</span>
              <span className="text-sm">dari</span>
              <span className="text-sm font-medium">{meta.totalPages}</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onQueryChange?.({
                ...query,
                page: (query.page || 1) + 1,
              })}
              disabled={loading || !meta.hasNextPage}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onQueryChange?.({
                ...query,
                page: meta.totalPages,
              })}
              disabled={loading || !meta.hasNextPage}
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
