'use client';

import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown, MoreHorizontal, Eye, Edit, Trash2, Zap, UserX, UserCheck, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Supplier, SupplierStatus } from '@/types/supplier';
import {
  getSupplierTypeLabel,
  getSupplierStatusLabel,
  getSupplierStatusColor,
  getSupplierTypeColor,
} from '@/lib/constants/supplier';
import { AlertDialogWrapper } from '@/components/alert-dialog-wrapper';

interface ColumnActionsProps {
  supplier: Supplier;
  onView?: (supplier: Supplier) => void;
  onEdit?: (supplier: Supplier) => void;
  onQuickView?: (supplier: Supplier) => void;
  onActivate?: (supplier: Supplier) => void;
  onDeactivate?: (supplier: Supplier) => void;
  onHardDelete?: (supplier: Supplier) => void;
  // Loading states for async operations
  isActivating?: boolean;
  isDeactivating?: boolean;
  isHardDeleting?: boolean;
}

function ColumnActions({
  supplier,
  onView,
  onEdit,
  onQuickView,
  onActivate,
  onDeactivate,
  onHardDelete,
  isActivating = false,
  isDeactivating = false,
  isHardDeleting = false,
}: ColumnActionsProps) {
  const isActive = supplier.status === SupplierStatus.ACTIVE;
  const isInactive = supplier.status === SupplierStatus.INACTIVE;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Buka menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Aksi</DropdownMenuLabel>
        <DropdownMenuItem
          onClick={() => navigator.clipboard.writeText(supplier.id)}
        >
          Salin ID
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        {onQuickView && (
          <DropdownMenuItem onClick={() => onQuickView(supplier)}>
            <Zap className="mr-2 h-4 w-4" />
            Quick View
          </DropdownMenuItem>
        )}
        {onView && (
          <DropdownMenuItem onClick={() => onView(supplier)}>
            <Eye className="mr-2 h-4 w-4" />
            Lihat Detail
          </DropdownMenuItem>
        )}
        {onEdit && (
          <DropdownMenuItem onClick={() => onEdit(supplier)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </DropdownMenuItem>
        )}

        <DropdownMenuSeparator />

        {/* Status-based actions */}
        {isActive && onDeactivate && (
          <AlertDialogWrapper
            variant="warning"
            title="Nonaktifkan Supplier"
            description="Apakah Anda yakin ingin menonaktifkan supplier ini?"
            confirmText="Nonaktifkan"
            cancelText="Batal"
            pendingText="Menonaktifkan..."
            disabled={isDeactivating}
            handler={() => onDeactivate(supplier)}
          >
            <DropdownMenuItem
              className="text-orange-600 focus:text-orange-600"
              onSelect={(event) => {
                event.preventDefault();
              }}
            >
              <UserX className="mr-2 h-4 w-4" />
              Nonaktifkan
            </DropdownMenuItem>
          </AlertDialogWrapper>
        )}

        {isInactive && onActivate && (
          <AlertDialogWrapper
            variant="primary"
            title="Aktifkan Supplier"
            description="Apakah Anda yakin ingin mengaktifkan supplier ini?"
            confirmText="Aktifkan"
            cancelText="Batal"
            pendingText="Mengaktifkan..."
            disabled={isActivating}
            handler={() => onActivate(supplier)}
          >
            <DropdownMenuItem
              className="text-green-600 focus:text-green-600"
              onSelect={(event) => {
                event.preventDefault();
              }}
            >
              <UserCheck className="mr-2 h-4 w-4" />
              Aktifkan
            </DropdownMenuItem>
          </AlertDialogWrapper>
        )}

        {isInactive && onHardDelete && (
          <AlertDialogWrapper
            variant="destructive"
            title="Hapus Supplier Permanen"
            description={`Anda akan menghapus permanen supplier "${supplier.name}". Tindakan ini akan:

• Menghapus semua kontak supplier yang terkait secara permanen
• Menghilangkan semua dokumen supplier dan catatan pembayaran
• Menyebabkan inkonsistensi pada riwayat purchase order yang mereferensikan supplier ini
• Mempengaruhi pelaporan keuangan dan analisis vendor
• Berdampak pada operasional bisnis dan hubungan supplier

Tindakan ini TIDAK DAPAT DIBATALKAN dan dapat berdampak serius pada integritas data sistem.`}
            confirmText="Hapus Permanen"
            cancelText="Batal"
            pendingText="Menghapus..."
            requireConfirmationText="HAPUS"
            confirmationPlaceholder="Ketik 'HAPUS' untuk mengonfirmasi"
            disabled={isHardDeleting}
            handler={() => onHardDelete(supplier)}
          >
            <DropdownMenuItem
              className="text-red-600 focus:text-red-600"
              onSelect={(event) => {
                event.preventDefault();
              }}
            >
              <AlertTriangle className="mr-2 h-4 w-4" />
              Hapus Permanen
            </DropdownMenuItem>
          </AlertDialogWrapper>
        )}


      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export const createSupplierColumns = (
  onView?: (supplier: Supplier) => void,
  onEdit?: (supplier: Supplier) => void,
  onQuickView?: (supplier: Supplier) => void,
  onActivate?: (supplier: Supplier) => void,
  onDeactivate?: (supplier: Supplier) => void,
  onHardDelete?: (supplier: Supplier) => void,
  loadingStates?: {
    isActivating?: boolean;
    isDeactivating?: boolean;
    isHardDeleting?: boolean;
  }
): ColumnDef<Supplier>[] => [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Pilih semua"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Pilih baris"
        />
      ),
      enableSorting: false,
      enableHiding: false,
      size: 40,
    },
    {
      accessorKey: 'code',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 px-2 lg:px-3"
          >
            Kode
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => (
        <div className="font-medium text-sm">{row.getValue('code')}</div>
      ),
      size: 100,
    },
    {
      accessorKey: 'name',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 px-2 lg:px-3"
          >
            Nama Supplier
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const supplier = row.original;
        return (
          <div className="min-w-[250px] max-w-[350px]">
            <div className="font-medium text-sm" title={row.getValue('name') as string}>
              {row.getValue('name')}
            </div>
            {supplier.email && (
              <div className="text-xs text-muted-foreground" title={supplier.email}>
                {supplier.email}
              </div>
            )}
          </div>
        );
      },
      size: 350,
    },
    {
      accessorKey: 'type',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 px-2 lg:px-3"
          >
            Jenis
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const type = row.getValue('type') as Supplier['type'];
        return (
          <Badge variant="outline" className={`${getSupplierTypeColor(type)} text-xs`}>
            {getSupplierTypeLabel(type)}
          </Badge>
        );
      },
      size: 120,
    },
    {
      accessorKey: 'city',
      header: 'Kota',
      cell: ({ row }) => {
        const supplier = row.original;
        return (
          <div className="min-w-[150px] max-w-[200px]">
            {supplier.city && (
              <div className="text-sm" title={supplier.city}>
                {supplier.city}
              </div>
            )}
            {supplier.province && (
              <div className="text-xs text-muted-foreground" title={supplier.province}>
                {supplier.province}
              </div>
            )}
          </div>
        );
      },
      size: 200,
    },
    {
      accessorKey: 'phone',
      header: 'Telepon',
      cell: ({ row }) => {
        const phone = row.getValue('phone') as string;
        return phone ? (
          <div className="font-mono text-xs min-w-[160px]" title={phone}>
            {phone}
          </div>
        ) : (
          <div className="text-muted-foreground text-xs">-</div>
        );
      },
      size: 180,
    },
    {
      accessorKey: 'status',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 px-2 lg:px-3"
          >
            Status
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const status = row.getValue('status') as Supplier['status'];
        return (
          <Badge variant="outline" className={`${getSupplierStatusColor(status)} text-xs`}>
            {getSupplierStatusLabel(status)}
          </Badge>
        );
      },
      size: 100,
    },
    {
      accessorKey: 'createdAt',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 px-2 lg:px-3"
          >
            Dibuat
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const date = new Date(row.getValue('createdAt'));
        return (
          <div className="text-xs">
            {date.toLocaleDateString('id-ID', {
              day: '2-digit',
              month: 'short',
              year: 'numeric',
            })}
          </div>
        );
      },
      size: 100,
    },
    {
      id: 'actions',
      header: () => (
        <div className="flex items-center justify-center gap-1">
          <span>Aksi</span>
          <div className="w-1 h-1 bg-blue-500 rounded-full animate-pulse" title="Kolom ini selalu terlihat"></div>
        </div>
      ),
      enableHiding: false,
      cell: ({ row }) => {
        const supplier = row.original;
        return (
          <div className="flex justify-center min-w-[80px]">
            <ColumnActions
              supplier={supplier}
              onView={onView}
              onEdit={onEdit}
              onQuickView={onQuickView}
              onActivate={onActivate}
              onDeactivate={onDeactivate}
              onHardDelete={onHardDelete}
              isActivating={loadingStates?.isActivating}
              isDeactivating={loadingStates?.isDeactivating}
              isHardDeleting={loadingStates?.isHardDeleting}
            />
          </div>
        );
      },
      size: 80,
    },
  ];
