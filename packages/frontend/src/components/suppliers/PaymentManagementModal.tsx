'use client';

import { useState } from 'react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DollarSign,
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  TrendingUp,
  CreditCard,
  Clock,
  CheckCircle,
  AlertCircle,
  RefreshCw,
} from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  useSupplierPayments,
  useSupplierPaymentSummary,
  useCreateSupplierPayment,
  useUpdateSupplierPayment,
  useDeleteSupplierPayment,
  useGeneratePaymentReference,
} from '@/hooks/useSuppliers';
import { PaymentQueryParams, SupplierPayment, CreateSupplierPaymentDto, PaymentMethod } from '@/types/supplier';
import { PAYMENT_METHOD_OPTIONS, PAYMENT_STATUS_OPTIONS } from '@/lib/constants/supplier';
import { formatCurrency } from '@/lib/utils';
import { PaymentProofUpload } from './PaymentProofUpload';
import { LiveCurrencyInput } from '@/components/ui/currency-input';

interface PaymentManagementModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  supplierId: string;
  supplierName: string;
}

// Local form interface that uses number for amount (compatible with LiveCurrencyInput)
interface PaymentFormData extends Omit<CreateSupplierPaymentDto, 'amount'> {
  amount: number | undefined;
}

export function PaymentManagementModal({
  open,
  onOpenChange,
  supplierId,
  supplierName,
}: PaymentManagementModalProps) {
  const [query, setQuery] = useState<PaymentQueryParams>({
    page: 1,
    limit: 10,
  });
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingPayment, setEditingPayment] = useState<SupplierPayment | null>(null);
  const [viewingPayment, setViewingPayment] = useState<SupplierPayment | null>(null);
  const [activeTab, setActiveTab] = useState('summary');
  const [formData, setFormData] = useState<PaymentFormData>({
    amount: undefined, // Allow undefined for better UX with currency input
    paymentDate: new Date().toISOString().split('T')[0],
    paymentMethod: PaymentMethod.TRANSFER,
    status: 'PENDING',
    invoiceNumber: '',
    reference: '',
    notes: '',
  });

  // Queries and mutations
  const { data: paymentsData, isLoading } = useSupplierPayments(supplierId, query);
  const { data: summaryData } = useSupplierPaymentSummary(supplierId);
  const createMutation = useCreateSupplierPayment();
  const updateMutation = useUpdateSupplierPayment();
  const deleteMutation = useDeleteSupplierPayment();
  const generateReferenceMutation = useGeneratePaymentReference();

  // Generate reference number
  const handleGenerateReference = async () => {
    try {
      const result = await generateReferenceMutation.mutateAsync();
      setFormData(prev => ({ ...prev, reference: result.reference }));
      toast.success('Nomor referensi berhasil dibuat');
    } catch (error) {
      console.error('Failed to generate reference:', error);
      toast.error('Gagal membuat nomor referensi');
    }
  };

  // Auto-generate reference when creating new payment (not editing)
  const handleAddNewPayment = () => {
    setShowAddForm(true);
    setActiveTab('add');
    setEditingPayment(null);

    // Reset form and auto-generate reference
    setFormData({
      amount: undefined,
      paymentDate: new Date().toISOString().split('T')[0],
      paymentMethod: PaymentMethod.TRANSFER,
      status: 'PENDING',
      invoiceNumber: '',
      reference: '', // Will be auto-generated
      notes: '',
    });

    // Auto-generate reference for new payments
    handleGenerateReference();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Convert undefined amount to 0 for API
    const submitData = {
      ...formData,
      amount: formData.amount ?? 0
    };

    const operationPromise = editingPayment
      ? updateMutation.mutateAsync({
        supplierId,
        paymentId: editingPayment.id,
        data: submitData,
      })
      : createMutation.mutateAsync({
        supplierId,
        data: submitData,
      });

    toast.promise(operationPromise, {
      loading: editingPayment ? 'Memperbarui pembayaran...' : 'Menyimpan pembayaran...',
      success: () => {
        // Reset form
        const resetFormData = {
          amount: undefined,
          paymentDate: new Date().toISOString().split('T')[0],
          paymentMethod: PaymentMethod.TRANSFER,
          status: 'PENDING',
          invoiceNumber: '',
          reference: '',
          notes: '',
        };

        setFormData(resetFormData);
        setShowAddForm(false);
        setEditingPayment(null);

        // If it was a new payment creation, auto-generate reference for next payment
        if (!editingPayment) {
          // Small delay to ensure form is reset before generating new reference
          setTimeout(() => {
            handleGenerateReference();
          }, 100);
        }

        return editingPayment
          ? 'Pembayaran berhasil diperbarui'
          : 'Pembayaran berhasil disimpan';
      },
      error: (error) => {
        console.error('Payment operation failed:', error);
        return error?.response?.data?.message ||
          (editingPayment ? 'Gagal memperbarui pembayaran' : 'Gagal menyimpan pembayaran');
      },
    });
  };

  const handleEdit = (payment: SupplierPayment) => {
    setEditingPayment(payment);
    setFormData({
      amount: payment.amount,
      paymentDate: new Date(payment.paymentDate).toISOString().split('T')[0],
      paymentMethod: payment.paymentMethod,
      status: payment.status,
      invoiceNumber: payment.invoiceNumber || '',
      reference: payment.reference || '',
      notes: payment.notes || '',
      dueDate: payment.dueDate ? new Date(payment.dueDate).toISOString().split('T')[0] : undefined,
    });
    setShowAddForm(true);
    setActiveTab('add');
  };

  const handleDelete = async (paymentId: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus pembayaran ini?')) {
      const deletePromise = deleteMutation.mutateAsync({ supplierId, paymentId });

      toast.promise(deletePromise, {
        loading: 'Menghapus pembayaran...',
        success: 'Pembayaran berhasil dihapus',
        error: (error) => {
          console.error('Delete failed:', error);
          return error?.response?.data?.message || 'Gagal menghapus pembayaran. Silakan coba lagi.';
        },
      });
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { variant: 'secondary' as const, icon: Clock, color: 'text-yellow-600' },
      PAID: { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
      OVERDUE: { variant: 'destructive' as const, icon: AlertCircle, color: 'text-red-600' },
      CANCELLED: { variant: 'outline' as const, icon: AlertCircle, color: 'text-gray-600' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {PAYMENT_STATUS_OPTIONS.find(opt => opt.value === status)?.label || status}
      </Badge>
    );
  };

  const getPaymentMethodLabel = (method: string) => {
    return PAYMENT_METHOD_OPTIONS.find(opt => opt.value === method)?.label || method;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-7xl max-h-[95vh] overflow-y-auto p-4 sm:p-6 lg:p-8 ">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Manajemen Pembayaran - {supplierName}
          </DialogTitle>
          <DialogDescription>
            Kelola riwayat pembayaran, tambah pembayaran baru, dan lihat ringkasan keuangan
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 overflow-hidden">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="summary">Ringkasan</TabsTrigger>
            <TabsTrigger value="payments">Riwayat Pembayaran</TabsTrigger>
            <TabsTrigger value="details" disabled={!viewingPayment}>
              {viewingPayment ? 'Detail Pembayaran' : 'Pilih Pembayaran'}
            </TabsTrigger>
            <TabsTrigger value="add">Tambah Pembayaran</TabsTrigger>
          </TabsList>

          <TabsContent value="summary" className="space-y-4 overflow-auto max-h-[60vh]">
            {summaryData && (
              <>
                {/* Summary Cards */}
                <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
                  <Card className="border-l-4 border-l-green-500">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-green-50 rounded-lg">
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-xs font-medium text-muted-foreground">Total Dibayar</p>
                          <p className="text-lg font-bold text-green-600 truncate">
                            {formatCurrency(summaryData.summary.totalPaid)}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {summaryData.summary.countPaid} transaksi
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-l-4 border-l-yellow-500">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-yellow-50 rounded-lg">
                          <Clock className="h-5 w-5 text-yellow-600" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-xs font-medium text-muted-foreground">Menunggu</p>
                          <p className="text-lg font-bold text-yellow-600 truncate">
                            {formatCurrency(summaryData.summary.totalPending)}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {summaryData.summary.countPending} transaksi
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-l-4 border-l-red-500">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-red-50 rounded-lg">
                          <AlertCircle className="h-5 w-5 text-red-600" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-xs font-medium text-muted-foreground">Terlambat</p>
                          <p className="text-lg font-bold text-red-600 truncate">
                            {formatCurrency(summaryData.summary.totalOverdue)}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {summaryData.summary.countOverdue} transaksi
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-l-4 border-l-blue-500">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-blue-50 rounded-lg">
                          <TrendingUp className="h-5 w-5 text-blue-600" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-xs font-medium text-muted-foreground">Total Transaksi</p>
                          <p className="text-lg font-bold text-blue-600">
                            {summaryData.summary.countPaid + summaryData.summary.countPending + summaryData.summary.countOverdue}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Semua status
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Recent Payments */}
                <Card>
                  <CardHeader>
                    <CardTitle>Pembayaran Terbaru</CardTitle>
                    <CardDescription>
                      {summaryData.recentPayments.length === 0
                        ? 'Belum ada pembayaran terbaru'
                        : summaryData.recentPayments.length === 1
                          ? '1 pembayaran terakhir'
                          : `${summaryData.recentPayments.length} pembayaran terakhir`
                      }
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {summaryData.recentPayments.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        <CreditCard className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p className="text-lg font-medium">Belum ada pembayaran</p>
                        <p className="text-sm">Pembayaran terbaru akan muncul di sini</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {summaryData.recentPayments.map((payment) => (
                          <div key={payment.id} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="flex items-center gap-3">
                              <div className="p-2 bg-muted rounded-lg">
                                <CreditCard className="h-4 w-4" />
                              </div>
                              <div>
                                <p className="font-medium">{payment.invoiceNumber || 'Tanpa Nomor Invoice'}</p>
                                <p className="text-sm text-muted-foreground">
                                  {new Date(payment.paymentDate).toLocaleDateString('id-ID')} • {getPaymentMethodLabel(payment.paymentMethod)}
                                </p>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="font-medium">{formatCurrency(payment.amount)}</p>
                              {getStatusBadge(payment.status)}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Payment Methods Breakdown */}
                <Card>
                  <CardHeader>
                    <CardTitle>Metode Pembayaran</CardTitle>
                    <CardDescription>Breakdown berdasarkan metode pembayaran</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {summaryData.paymentsByMethod.map((item) => (
                        <div key={item.method} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <CreditCard className="h-4 w-4 text-muted-foreground" />
                            <span>{getPaymentMethodLabel(item.method)}</span>
                          </div>
                          <div className="text-right">
                            <p className="font-medium">{formatCurrency(item.amount)}</p>
                            <p className="text-sm text-muted-foreground">{item.count} transaksi</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </TabsContent>

          <TabsContent value="payments" className="space-y-4 overflow-auto max-h-[60vh]">
            {/* Search and Filter Controls */}
            <div className="space-y-4">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div className="flex gap-2 flex-wrap">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Cari pembayaran..."
                      value={query.search || ''}
                      onChange={(e) => setQuery(prev => ({ ...prev, search: e.target.value, page: 1 }))}
                      className="pl-10 w-64"
                    />
                  </div>
                  <Select
                    value={query.status || 'all'}
                    onValueChange={(value) => setQuery(prev => ({
                      ...prev,
                      status: value === 'all' ? undefined : value,
                      page: 1
                    }))}
                  >
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Semua Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Semua Status</SelectItem>
                      {PAYMENT_STATUS_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select
                    value={query.paymentMethod || 'all'}
                    onValueChange={(value) => setQuery(prev => ({
                      ...prev,
                      paymentMethod: value === 'all' ? undefined : value as any,
                      page: 1
                    }))}
                  >
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Semua Metode" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Semua Metode</SelectItem>
                      {PAYMENT_METHOD_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <Button onClick={handleAddNewPayment}>
                  <Plus className="h-4 w-4 mr-2" />
                  Tambah Pembayaran
                </Button>
              </div>

              {/* Date Range Filter */}
              <div className="flex gap-2 items-center">
                <Label className="text-sm font-medium">Rentang Tanggal:</Label>
                <Input
                  type="date"
                  placeholder="Dari tanggal"
                  value={query.dateFrom || ''}
                  onChange={(e) => setQuery(prev => ({ ...prev, dateFrom: e.target.value || undefined, page: 1 }))}
                  className="w-40"
                />
                <span className="text-sm text-muted-foreground">sampai</span>
                <Input
                  type="date"
                  placeholder="Sampai tanggal"
                  value={query.dateTo || ''}
                  onChange={(e) => setQuery(prev => ({ ...prev, dateTo: e.target.value || undefined, page: 1 }))}
                  className="w-40"
                />
                {(query.dateFrom || query.dateTo) && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setQuery(prev => ({ ...prev, dateFrom: undefined, dateTo: undefined, page: 1 }))}
                  >
                    Reset
                  </Button>
                )}
              </div>
            </div>

            {/* Payments Table */}
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Invoice</TableHead>
                    <TableHead>Tanggal</TableHead>
                    <TableHead>Jumlah</TableHead>
                    <TableHead>Metode</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    Array.from({ length: 5 }).map((_, index) => (
                      <TableRow key={index}>
                        <TableCell colSpan={6}>
                          <div className="h-4 bg-muted animate-pulse rounded"></div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : paymentsData?.data.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                        Belum ada data pembayaran
                      </TableCell>
                    </TableRow>
                  ) : (
                    paymentsData?.data.map((payment) => (
                      <TableRow key={payment.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{payment.invoiceNumber || '-'}</p>
                            {payment.reference && (
                              <p className="text-sm text-muted-foreground">Ref: {payment.reference}</p>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {new Date(payment.paymentDate).toLocaleDateString('id-ID')}
                        </TableCell>
                        <TableCell className="font-medium">
                          {formatCurrency(payment.amount)}
                        </TableCell>
                        <TableCell>
                          {getPaymentMethodLabel(payment.paymentMethod)}
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(payment.status)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setViewingPayment(payment);
                                setActiveTab('details');
                              }}
                              title="Lihat Detail"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEdit(payment)}
                              title="Edit"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(payment.id)}
                              disabled={deleteMutation.isPending}
                              title="Hapus"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            {paymentsData?.meta && paymentsData.meta.totalPages > 1 && (
              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground">
                  Menampilkan {((paymentsData.meta.page - 1) * paymentsData.meta.limit) + 1} - {Math.min(paymentsData.meta.page * paymentsData.meta.limit, paymentsData.meta.total)} dari {paymentsData.meta.total} pembayaran
                </p>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setQuery(prev => ({ ...prev, page: prev.page! - 1 }))}
                    disabled={!paymentsData.meta.hasPreviousPage}
                  >
                    Sebelumnya
                  </Button>
                  <span className="text-sm">
                    Halaman {paymentsData.meta.page} dari {paymentsData.meta.totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setQuery(prev => ({ ...prev, page: prev.page! + 1 }))}
                    disabled={!paymentsData.meta.hasNextPage}
                  >
                    Selanjutnya
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="details" className="space-y-4">
            {viewingPayment ? (
              <div className="space-y-6">
                {/* Payment Details Card */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CreditCard className="h-5 w-5" />
                      Detail Pembayaran
                    </CardTitle>
                    <CardDescription>
                      Informasi lengkap pembayaran dan bukti pembayaran
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid gap-4 sm:grid-cols-2">
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Nomor Invoice</Label>
                        <p className="font-medium">{viewingPayment.invoiceNumber || '-'}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Referensi</Label>
                        <p className="font-medium">{viewingPayment.reference || '-'}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Jumlah</Label>
                        <p className="font-medium text-lg">{formatCurrency(viewingPayment.amount)}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Metode Pembayaran</Label>
                        <p className="font-medium">{getPaymentMethodLabel(viewingPayment.paymentMethod)}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Tanggal Pembayaran</Label>
                        <p className="font-medium">
                          {new Date(viewingPayment.paymentDate).toLocaleDateString('id-ID', {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Status</Label>
                        <div className="mt-1">
                          {getStatusBadge(viewingPayment.status)}
                        </div>
                      </div>
                      {viewingPayment.dueDate && (
                        <div>
                          <Label className="text-sm font-medium text-muted-foreground">Tanggal Jatuh Tempo</Label>
                          <p className="font-medium">
                            {new Date(viewingPayment.dueDate).toLocaleDateString('id-ID', {
                              weekday: 'long',
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </p>
                        </div>
                      )}
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Dibuat</Label>
                        <p className="font-medium">
                          {new Date(viewingPayment.createdAt).toLocaleDateString('id-ID', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </p>
                      </div>
                    </div>
                    {viewingPayment.notes && (
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Catatan</Label>
                        <p className="mt-1 p-3 bg-muted rounded-lg">{viewingPayment.notes}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Payment Proof Section */}
                <PaymentProofUpload
                  supplierId={supplierId}
                  paymentId={viewingPayment.id}
                  paymentReference={viewingPayment.reference || viewingPayment.invoiceNumber}
                  onProofUploaded={() => {
                    // Optionally refresh data or show success message
                  }}
                />

                {/* Actions */}
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => handleEdit(viewingPayment)}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Pembayaran
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setViewingPayment(null);
                      setActiveTab('payments');
                    }}
                  >
                    Kembali ke Daftar
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <CreditCard className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium">Pilih pembayaran untuk melihat detail</p>
                <p className="text-sm">Klik tombol mata pada tabel pembayaran untuk melihat detail lengkap</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="add" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>
                  {editingPayment ? 'Edit Pembayaran' : 'Tambah Pembayaran Baru'}
                </CardTitle>
                <CardDescription>
                  {editingPayment ? 'Perbarui informasi pembayaran' : 'Masukkan detail pembayaran baru'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid gap-4 sm:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="amount">Jumlah Pembayaran *</Label>
                      <LiveCurrencyInput
                        id="amount"
                        value={formData.amount}
                        onChange={(value) => {
                          setFormData(prev => ({
                            ...prev,
                            amount: value
                          }));
                        }}
                        placeholder="Masukkan jumlah pembayaran"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="paymentDate">Tanggal Pembayaran *</Label>
                      <Input
                        id="paymentDate"
                        type="date"
                        value={formData.paymentDate}
                        onChange={(e) => setFormData(prev => ({ ...prev, paymentDate: e.target.value }))}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="paymentMethod">Metode Pembayaran *</Label>
                      <Select
                        value={formData.paymentMethod}
                        onValueChange={(value) => setFormData(prev => ({ ...prev, paymentMethod: value as any }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih metode pembayaran" />
                        </SelectTrigger>
                        <SelectContent>
                          {PAYMENT_METHOD_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="status">Status *</Label>
                      <Select
                        value={formData.status}
                        onValueChange={(value) => setFormData(prev => ({ ...prev, status: value as any }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih status" />
                        </SelectTrigger>
                        <SelectContent>
                          {PAYMENT_STATUS_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="invoiceNumber">Nomor Invoice</Label>
                      <Input
                        id="invoiceNumber"
                        value={formData.invoiceNumber}
                        onChange={(e) => setFormData(prev => ({ ...prev, invoiceNumber: e.target.value }))}
                        placeholder="INV-001"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="reference">Nomor Referensi</Label>
                      <div className="flex gap-2">
                        <Input
                          id="reference"
                          value={formData.reference}
                          onChange={(e) => setFormData(prev => ({ ...prev, reference: e.target.value }))}
                          placeholder="PAY-ASMSUP-100624-001"
                          className="flex-1"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={handleGenerateReference}
                          disabled={generateReferenceMutation.isPending}
                          className="px-3"
                        >
                          {generateReferenceMutation.isPending ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                          ) : (
                            <RefreshCw className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Format: PAY-{'{INISIAL}'}SUP-DDMMYY-{'{URUTAN}'} • Klik tombol untuk membuat otomatis
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="dueDate">Tanggal Jatuh Tempo</Label>
                      <Input
                        id="dueDate"
                        type="date"
                        value={formData.dueDate || ''}
                        onChange={(e) => setFormData(prev => ({ ...prev, dueDate: e.target.value || undefined }))}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="notes">Catatan</Label>
                    <Textarea
                      id="notes"
                      value={formData.notes}
                      onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                      placeholder="Catatan tambahan untuk pembayaran ini"
                      rows={3}
                    />
                  </div>

                  <div className="flex gap-2">
                    <Button
                      type="submit"
                      disabled={createMutation.isPending || updateMutation.isPending}
                    >
                      {createMutation.isPending || updateMutation.isPending ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          {editingPayment ? 'Memperbarui...' : 'Menyimpan...'}
                        </>
                      ) : (
                        <>
                          {editingPayment ? 'Perbarui Pembayaran' : 'Simpan Pembayaran'}
                        </>
                      )}
                    </Button>

                    {(showAddForm || editingPayment) && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          setShowAddForm(false);
                          setEditingPayment(null);
                          setFormData({
                            amount: undefined,
                            paymentDate: new Date().toISOString().split('T')[0],
                            paymentMethod: PaymentMethod.TRANSFER,
                            status: 'PENDING',
                            invoiceNumber: '',
                            reference: '',
                            notes: '',
                          });
                        }}
                      >
                        Batal
                      </Button>
                    )}
                  </div>
                </form>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
