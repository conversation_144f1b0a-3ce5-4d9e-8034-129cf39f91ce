'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Calculator, Plus, Minus, AlertTriangle } from 'lucide-react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

import { InventoryItem, StockAdjustmentType, STOCK_ADJUSTMENT_REASONS } from '@/types/inventory';
import { useAdjustStock } from '@/hooks/useInventory';
import { formatCurrency } from '@/lib/utils';

// Form validation schema
const stockAdjustmentSchema = z.object({
  adjustmentType: z.nativeEnum(StockAdjustmentType, {
    required_error: 'Jenis penyesuaian wajib dipilih',
  }),
  quantity: z.number().min(1, 'Jumlah harus lebih dari 0'),
  reason: z.string().min(1, 'Alasan penyesuaian wajib diisi'),
  notes: z.string().optional(),
}).refine((data) => {
  // Additional validation can be added here if needed
  return true;
}, {
  message: 'Data penyesuaian tidak valid',
});

type StockAdjustmentFormData = z.infer<typeof stockAdjustmentSchema>;

interface StockAdjustmentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  inventoryItem: InventoryItem | null;
}

export function StockAdjustmentDialog({
  open,
  onOpenChange,
  inventoryItem,
}: StockAdjustmentDialogProps) {
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [formData, setFormData] = useState<StockAdjustmentFormData | null>(null);

  const adjustStockMutation = useAdjustStock();

  const form = useForm<StockAdjustmentFormData>({
    resolver: zodResolver(stockAdjustmentSchema),
    defaultValues: {
      adjustmentType: StockAdjustmentType.INCREASE,
      quantity: 1,
      reason: '',
      notes: '',
    },
  });

  // Reset form when dialog opens/closes or inventory item changes
  useEffect(() => {
    if (open && inventoryItem) {
      form.reset({
        adjustmentType: StockAdjustmentType.INCREASE,
        quantity: 1,
        reason: '',
        notes: '',
      });
      setShowConfirmation(false);
      setFormData(null);
    }
  }, [open, inventoryItem, form]);

  // Watch form values for real-time calculation
  const watchedType = form.watch('adjustmentType');
  const watchedQuantity = form.watch('quantity');

  // Calculate new stock quantity
  const calculateNewStock = () => {
    if (!inventoryItem || !watchedQuantity) return inventoryItem?.quantityOnHand || 0;

    const currentStock = inventoryItem.quantityOnHand;
    const adjustment = watchedType === StockAdjustmentType.INCREASE
      ? watchedQuantity
      : -watchedQuantity;

    return Math.max(0, currentStock + adjustment);
  };

  const newStockQuantity = calculateNewStock();
  const isStockDecrease = watchedType === StockAdjustmentType.DECREASE;
  const willBecomeZero = newStockQuantity === 0;
  const isInsufficientStock = isStockDecrease && watchedQuantity > (inventoryItem?.quantityOnHand || 0);

  // Validate that decrease doesn't result in negative stock
  useEffect(() => {
    if (isInsufficientStock) {
      form.setError('quantity', {
        type: 'manual',
        message: `Jumlah tidak boleh melebihi stok saat ini (${inventoryItem?.quantityOnHand || 0})`,
      });
    } else {
      form.clearErrors('quantity');
    }
  }, [isInsufficientStock, inventoryItem?.quantityOnHand, form]);

  const onSubmit = (data: StockAdjustmentFormData) => {
    setFormData(data);
    setShowConfirmation(true);
  };

  const handleConfirmAdjustment = async () => {
    if (!inventoryItem || !formData) return;

    try {
      // Convert form data to API format
      // Backend expects quantity to be positive for increase, negative for decrease
      const adjustmentData = {
        quantity: formData.adjustmentType === StockAdjustmentType.INCREASE
          ? formData.quantity
          : -formData.quantity,
        reason: formData.reason,
        notes: formData.notes || undefined,
      };

      const result = await adjustStockMutation.mutateAsync({
        id: inventoryItem.id,
        data: adjustmentData,
      });

      // Close dialog on success
      onOpenChange(false);
      setShowConfirmation(false);
      setFormData(null);
    } catch (error) {
      // Error handling is done in the mutation hook
      console.error('Error adjusting stock:', error);
      setShowConfirmation(false);
    }
  };

  const handleCancel = () => {
    setShowConfirmation(false);
    setFormData(null);
  };

  if (!inventoryItem) return null;

  return (
    <>
      <Dialog open={open && !showConfirmation} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              Penyesuaian Stok
            </DialogTitle>
            <DialogDescription>
              Sesuaikan jumlah stok untuk {inventoryItem.product?.name || 'produk ini'}
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Current Stock Display */}
              <Card>
                <CardContent className="pt-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                    <div>
                      <Label className="text-sm text-muted-foreground">Stok Saat Ini</Label>
                      <div className="text-2xl font-bold text-blue-600">
                        {inventoryItem.quantityOnHand}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {inventoryItem.unit?.name || 'unit'}
                      </div>
                    </div>

                    <div className="flex items-center justify-center">
                      <div className="flex items-center gap-2">
                        {watchedType === StockAdjustmentType.INCREASE ? (
                          <Plus className="h-4 w-4 text-green-600" />
                        ) : (
                          <Minus className="h-4 w-4 text-red-600" />
                        )}
                        <span className="text-lg font-medium">
                          {watchedQuantity || 0}
                        </span>
                      </div>
                    </div>

                    <div>
                      <Label className="text-sm text-muted-foreground">Stok Baru</Label>
                      <div className={`text-2xl font-bold ${willBecomeZero ? 'text-orange-600' :
                        isStockDecrease ? 'text-red-600' : 'text-green-600'
                        }`}>
                        {newStockQuantity}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {inventoryItem.unit?.name || 'unit'}
                      </div>
                    </div>
                  </div>

                  {willBecomeZero && (
                    <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                      <div className="flex items-center gap-2 text-orange-800">
                        <AlertTriangle className="h-4 w-4" />
                        <span className="text-sm font-medium">
                          Peringatan: Stok akan menjadi 0 setelah penyesuaian
                        </span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Separator />

              {/* Adjustment Type */}
              <FormField
                control={form.control}
                name="adjustmentType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Jenis Penyesuaian *</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="flex flex-col space-y-2"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value={StockAdjustmentType.INCREASE} id="increase" />
                          <Label htmlFor="increase" className="flex items-center gap-2 cursor-pointer">
                            <Plus className="h-4 w-4 text-green-600" />
                            <span>Tambah Stok</span>
                            <Badge variant="outline" className="text-green-600 border-green-600">
                              Increase
                            </Badge>
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value={StockAdjustmentType.DECREASE} id="decrease" />
                          <Label htmlFor="decrease" className="flex items-center gap-2 cursor-pointer">
                            <Minus className="h-4 w-4 text-red-600" />
                            <span>Kurangi Stok</span>
                            <Badge variant="outline" className="text-red-600 border-red-600">
                              Decrease
                            </Badge>
                          </Label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Adjustment Quantity */}
              <FormField
                control={form.control}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Jumlah Penyesuaian *</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        step="1"
                        placeholder="Masukkan jumlah"
                        {...field}
                        onChange={(e) => {
                          const value = e.target.value;
                          const numValue = value === '' ? undefined : parseInt(value) || 0;
                          field.onChange(numValue);
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      Jumlah yang akan {watchedType === StockAdjustmentType.INCREASE ? 'ditambahkan ke' : 'dikurangi dari'} stok saat ini
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Adjustment Reason */}
              <FormField
                control={form.control}
                name="reason"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Alasan Penyesuaian *</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih alasan penyesuaian" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {STOCK_ADJUSTMENT_REASONS.map((reason) => (
                          <SelectItem key={reason} value={reason}>
                            {reason}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Pilih alasan untuk penyesuaian stok ini
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Additional Notes */}
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Catatan Tambahan</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Catatan tambahan tentang penyesuaian stok (opsional)"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Informasi tambahan tentang alasan penyesuaian
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Form Actions */}
              <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={adjustStockMutation.isPending}
                >
                  Batal
                </Button>
                <Button
                  type="submit"
                  disabled={adjustStockMutation.isPending || isInsufficientStock}
                  className={watchedType === StockAdjustmentType.DECREASE ? 'bg-red-600 hover:bg-red-700' : ''}
                >
                  {watchedType === StockAdjustmentType.INCREASE ? 'Tambah' : 'Kurangi'} Stok
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog */}
      <AlertDialog open={showConfirmation} onOpenChange={setShowConfirmation}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {formData?.adjustmentType === StockAdjustmentType.INCREASE ? 'Konfirmasi Penambahan Stok' : 'Konfirmasi Pengurangan Stok'}
            </AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div className="space-y-3">
                <p>
                  Apakah Anda yakin ingin {formData?.adjustmentType === StockAdjustmentType.INCREASE ? 'menambah' : 'mengurangi'} stok
                  untuk <strong>{inventoryItem.product?.name}</strong>?
                </p>

                <div className="bg-muted p-3 rounded-lg space-y-2">
                  <div className="flex justify-between">
                    <span>Stok Saat Ini:</span>
                    <span className="font-medium">{inventoryItem.quantityOnHand} {inventoryItem.unit?.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Penyesuaian:</span>
                    <span className={`font-medium ${formData?.adjustmentType === StockAdjustmentType.INCREASE ? 'text-green-600' : 'text-red-600'}`}>
                      {formData?.adjustmentType === StockAdjustmentType.INCREASE ? '+' : '-'}{formData?.quantity} {inventoryItem.unit?.name}
                    </span>
                  </div>
                  <div className="flex justify-between border-t pt-2">
                    <span>Stok Baru:</span>
                    <span className="font-bold">{newStockQuantity} {inventoryItem.unit?.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Alasan:</span>
                    <span className="font-medium">{formData?.reason}</span>
                  </div>
                  {formData?.notes && (
                    <div className="flex justify-between">
                      <span>Catatan:</span>
                      <span className="font-medium">{formData.notes}</span>
                    </div>
                  )}
                </div>

                {willBecomeZero && (
                  <div className="bg-orange-50 border border-orange-200 p-3 rounded-lg">
                    <div className="flex items-center gap-2 text-orange-800">
                      <AlertTriangle className="h-4 w-4" />
                      <span className="text-sm font-medium">
                        Peringatan: Stok akan menjadi 0 setelah penyesuaian
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={adjustStockMutation.isPending}>
              Batal
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmAdjustment}
              disabled={adjustStockMutation.isPending}
              className={formData?.adjustmentType === StockAdjustmentType.DECREASE ? 'bg-red-600 hover:bg-red-700' : ''}
            >
              {adjustStockMutation.isPending
                ? (formData?.adjustmentType === StockAdjustmentType.INCREASE ? 'Menambah...' : 'Mengurangi...')
                : (formData?.adjustmentType === StockAdjustmentType.INCREASE ? 'Tambah Stok' : 'Kurangi Stok')
              }
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
