'use client';

import { ColumnDef } from '@tanstack/react-table';
import { MoreHorizontal, Eye, Edit, Trash2, UserX, UserCheck, Calculator, TrendingDown, Clock, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { AlertDialogWrapper } from '@/components/alert-dialog-wrapper';
import { InventoryItem } from '@/types/inventory';
import {
  formatCurrency,
  formatDate,
  getStockLevelStatus,
  getStockLevelColor,
  getExpiryStatus,
  getExpiryStatusColor,
  STOCK_THRESHOLDS,
  formatQuantityWithUnit,
  formatStockWithAllocation
} from '@/lib/constants/inventory';
import { Separator } from '../ui/separator';

interface ColumnActions {
  onView: (item: InventoryItem) => void;
  onEdit: (item: InventoryItem) => void;
  onQuickView: (item: InventoryItem) => void;
  onAdjustStock: (item: InventoryItem) => void;
  onViewAllocationHistory: (item: InventoryItem) => void;
  onActivate: (item: InventoryItem) => void;
  onDeactivate: (item: InventoryItem) => void;
  onHardDelete: (item: InventoryItem) => void;
}

interface LoadingStates {
  isActivating: boolean;
  isDeactivating: boolean;
  isHardDeleting: boolean;
}

export function createInventoryColumns(
  actions: ColumnActions,
  loadingStates: LoadingStates
): ColumnDef<InventoryItem>[] {
  return [
    {
      accessorKey: 'product',
      header: 'Produk',
      cell: ({ row }) => {
        const item = row.original;
        return (
          <div className="space-y-1">
            <div className="font-medium text-sm">{item.product?.name || 'N/A'}</div>
            <div className="text-xs text-muted-foreground">
              {item.product?.code || 'N/A'}
            </div>
            {item.product?.category && (
              <Badge variant="outline" className="text-xs">
                {item.product.category}
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'batchNumber',
      header: 'Batch',
      cell: ({ row }) => {
        const batchNumber = row.getValue('batchNumber') as string;
        return (
          <div className="text-sm">
            {batchNumber || (
              <span className="text-muted-foreground italic">Tidak ada</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'quantityOnHand',
      header: 'Stok',
      cell: ({ row }) => {
        const item = row.original;
        const quantityOnHand = item.quantityOnHand;
        const quantityAllocated = item.quantityAllocated || 0;
        const availableQuantity = Math.max(0, quantityOnHand - quantityAllocated);
        const status = getStockLevelStatus(availableQuantity, STOCK_THRESHOLDS.LOW_STOCK);
        const colorClass = getStockLevelColor(status);

        const stockInfo = formatStockWithAllocation(
          quantityOnHand,
          quantityAllocated,
          item.unit?.name,
          item.unit?.abbreviation
        );

        return (
          <TooltipProvider>
            <div className="space-y-1">
              {stockInfo.hasAllocation ? (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="cursor-help">
                      <div className="font-medium text-sm flex items-center gap-1">
                        <span>{stockInfo.availableStock.toLocaleString('id-ID')}</span>
                        <Info className="h-3 w-3 text-muted-foreground" />
                      </div>
                      <div className="text-xs text-muted-foreground">
                        dari {stockInfo.totalStock.toLocaleString('id-ID')} {item.unit?.abbreviation || item.unit?.name || 'unit'}
                      </div>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="top" className="max-w-xs">
                    <div className="text-xs">
                      <div className="font-medium mb-1">Detail Stok:</div>
                      <div>• Stok tersedia untuk alokasi baru: {stockInfo.availableStock.toLocaleString('id-ID')} {item.unit?.abbreviation || item.unit?.name || 'unit'}</div>
                      <div>• Stok dialokasikan (perencanaan): {quantityAllocated.toLocaleString('id-ID')} {item.unit?.abbreviation || item.unit?.name || 'unit'}</div>
                      <div>• Total stok fisik: {stockInfo.totalStock.toLocaleString('id-ID')} {item.unit?.abbreviation || item.unit?.name || 'unit'}</div>
                      <div className="mt-1 text-muted-foreground">
                        Semua stok fisik dapat dijual. Alokasi hanya untuk perencanaan FEFO/FIFO.
                      </div>
                    </div>
                  </TooltipContent>
                </Tooltip>
              ) : (
                <div className="font-medium text-sm">
                  {quantityOnHand.toLocaleString('id-ID')} {item.unit?.abbreviation || item.unit?.name || 'unit'}
                </div>
              )}
              <Badge variant="outline" className={`text-xs ${colorClass}`}>
                {status === 'out-of-stock' && 'Habis'}
                {status === 'low' && 'Rendah'}
                {status === 'normal' && 'Normal'}
                {status === 'overstock' && 'Berlebih'}
              </Badge>
            </div>
          </TooltipProvider>
        );
      },
    },
    {
      accessorKey: 'allocation',
      header: () => (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center gap-1 cursor-help">
                <span>Alokasi</span>
                <Info className="h-3 w-3 text-muted-foreground" />
              </div>
            </TooltipTrigger>
            <TooltipContent side="top" className="max-w-xs">
              <div className="text-xs">
                <div className="font-medium mb-1">Alokasi Stok</div>
                <div>Jumlah stok yang direservasi untuk perencanaan inventori menggunakan metode FEFO/FIFO.</div>
                <Separator className="my-2 bg-slate-600" />
                <div className="mt-1">
                  Stok yang dialokasikan masih dapat dijual melalui POS. Alokasi hanya untuk perencanaan, bukan pemblokiran penjualan.
                </div>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ),
      cell: ({ row }) => {
        const item = row.original;
        const quantityAllocated = item.quantityAllocated || 0;
        const lastAllocation = item.allocationSummary?.lastAllocation;

        if (quantityAllocated === 0) {
          return (
            <div className="text-xs text-muted-foreground italic">
              Belum ada alokasi
            </div>
          );
        }

        const unitDisplay = item.unit?.abbreviation || item.unit?.name || 'unit';
        const formattedQuantity = formatQuantityWithUnit(
          quantityAllocated,
          item.unit?.name,
          item.unit?.abbreviation
        );

        return (
          <TooltipProvider>
            <div className="space-y-1">
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-1 cursor-help">
                    <TrendingDown className="h-3 w-3 text-orange-500" />
                    <span className="text-sm font-medium">{formattedQuantity}</span>
                  </div>
                </TooltipTrigger>
                <TooltipContent side="top" className="max-w-xs">
                  <div className="text-xs">
                    <div className="font-medium mb-1">Detail Alokasi:</div>
                    <div>• Jumlah dialokasikan: {formattedQuantity}</div>
                    <div>• Stok tersedia untuk alokasi baru: {Math.max(0, item.quantityOnHand - quantityAllocated).toLocaleString('id-ID')} {unitDisplay}</div>
                    <div>• Total stok fisik: {item.quantityOnHand.toLocaleString('id-ID')} {unitDisplay}</div>
                    {lastAllocation && (
                      <div className="mt-1">• Alokasi terakhir: {formatDate(lastAllocation)}</div>
                    )}
                    <div className="mt-1 text-muted-foreground">
                      Alokasi = perencanaan inventori. Stok tetap dapat dijual melalui POS.
                    </div>
                  </div>
                </TooltipContent>
              </Tooltip>
              {lastAllocation && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  <span>{formatDate(lastAllocation)}</span>
                </div>
              )}
            </div>
          </TooltipProvider>
        );
      },
    },
    {
      accessorKey: 'unit',
      header: 'Satuan',
      cell: ({ row }) => {
        const item = row.original;
        return (
          <div className="text-sm">
            {item.unit?.name || 'N/A'}
            {item.unit?.abbreviation && (
              <div className="text-xs text-muted-foreground">
                ({item.unit.abbreviation})
              </div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'costPrice',
      header: 'Harga Beli',
      cell: ({ row }) => {
        const costPrice = row.getValue('costPrice') as number;
        return (
          <div className="text-sm font-medium">
            {formatCurrency(costPrice)}
          </div>
        );
      },
    },
    {
      accessorKey: 'sellingPrice',
      header: 'Harga Jual',
      cell: ({ row }) => {
        const sellingPrice = row.getValue('sellingPrice') as number;
        return (
          <div className="text-sm font-medium">
            {sellingPrice ? formatCurrency(sellingPrice) : (
              <span className="text-muted-foreground italic">Belum diset</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'expiryDate',
      header: 'Kedaluwarsa',
      cell: ({ row }) => {
        const expiryDate = row.getValue('expiryDate') as string;

        if (!expiryDate) {
          return (
            <span className="text-muted-foreground italic text-sm">
              Tidak ada
            </span>
          );
        }

        const status = getExpiryStatus(expiryDate);
        const colorClass = getExpiryStatusColor(status);

        return (
          <div className="space-y-1">
            <div className="text-sm">{formatDate(expiryDate)}</div>
            <Badge variant="outline" className={`text-xs ${colorClass}`}>
              {status === 'expired' && 'Kedaluwarsa'}
              {status === 'expiring-soon' && 'Akan Kedaluwarsa'}
              {status === 'good' && 'Masih Valid'}
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: 'supplier',
      header: 'Supplier',
      cell: ({ row }) => {
        const item = row.original;
        return (
          <div className="text-sm">
            {item.supplier?.name || (
              <span className="text-muted-foreground italic">Tidak ada</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'location',
      header: 'Lokasi',
      cell: ({ row }) => {
        const location = row.getValue('location') as string;
        return (
          <div className="text-sm">
            {location || (
              <span className="text-muted-foreground italic">Tidak diset</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'isActive',
      header: 'Status',
      cell: ({ row }) => {
        const isActive = row.getValue('isActive') as boolean;
        return (
          <Badge variant={isActive ? 'default' : 'secondary'}>
            {isActive ? 'Aktif' : 'Tidak Aktif'}
          </Badge>
        );
      },
    },
    {
      id: 'actions',
      header: 'Aksi',
      cell: ({ row }) => {
        const item = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Buka menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuLabel>Aksi</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => actions.onQuickView(item)}>
                <Eye className="mr-2 h-4 w-4" />
                Lihat Cepat
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => actions.onView(item)}>
                <Eye className="mr-2 h-4 w-4" />
                Lihat Detail
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => actions.onEdit(item)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              {item.isActive && (
                <DropdownMenuItem onClick={() => actions.onAdjustStock(item)}>
                  <Calculator className="mr-2 h-4 w-4" />
                  Sesuaikan Stok
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onClick={() => actions.onViewAllocationHistory(item)}>
                <TrendingDown className="mr-2 h-4 w-4" />
                Riwayat Alokasi
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {item.isActive ? (
                <AlertDialogWrapper
                  variant="warning"
                  title="Nonaktifkan Item Inventori"
                  description="Apakah Anda yakin ingin menonaktifkan item inventori ini?"
                  confirmText="Nonaktifkan"
                  cancelText="Batal"
                  pendingText="Menonaktifkan..."
                  disabled={loadingStates.isDeactivating}
                  handler={() => actions.onDeactivate(item)}
                >
                  <DropdownMenuItem
                    className="text-orange-600 focus:text-orange-600"
                    onSelect={(event) => {
                      event.preventDefault();
                    }}
                  >
                    <UserX className="mr-2 h-4 w-4" />
                    Nonaktifkan
                  </DropdownMenuItem>
                </AlertDialogWrapper>
              ) : (
                <AlertDialogWrapper
                  variant="primary"
                  title="Aktifkan Item Inventori"
                  description="Apakah Anda yakin ingin mengaktifkan item inventori ini?"
                  confirmText="Aktifkan"
                  cancelText="Batal"
                  pendingText="Mengaktifkan..."
                  disabled={loadingStates.isActivating}
                  handler={() => actions.onActivate(item)}
                >
                  <DropdownMenuItem
                    className="text-green-600 focus:text-green-600"
                    onSelect={(event) => {
                      event.preventDefault();
                    }}
                  >
                    <UserCheck className="mr-2 h-4 w-4" />
                    Aktifkan
                  </DropdownMenuItem>
                </AlertDialogWrapper>
              )}
              <DropdownMenuSeparator />
              {!item.isActive && (
                <AlertDialogWrapper
                  variant="destructive"
                  title="Hapus Item Inventori Permanen"
                  description={`Anda akan menghapus permanen item inventori "${item.product?.name || 'N/A'}" dengan batch "${item.batchNumber || 'N/A'}". Tindakan ini akan:

• Menghapus semua data item inventori secara permanen
• Menghapus riwayat transaksi terkait item ini
• Menghapus data batch dan informasi kedaluwarsa
• Menghapus semua catatan pergerakan stok

Tindakan ini TIDAK DAPAT DIBATALKAN dan dapat berdampak serius pada integritas data sistem.`}
                  confirmText="Hapus Permanen"
                  cancelText="Batal"
                  pendingText="Menghapus..."
                  requireConfirmationText="HAPUS"
                  confirmationPlaceholder="Ketik 'HAPUS' untuk mengonfirmasi"
                  disabled={loadingStates.isHardDeleting}
                  handler={() => actions.onHardDelete(item)}
                >
                  <DropdownMenuItem
                    className="text-red-600 focus:text-red-600"
                    onSelect={(event) => {
                      event.preventDefault();
                    }}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Hapus Permanen
                  </DropdownMenuItem>
                </AlertDialogWrapper>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];
}
