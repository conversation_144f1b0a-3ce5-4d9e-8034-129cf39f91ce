'use client';

import { useRouter } from 'next/navigation';
import { <PERSON><PERSON><PERSON><PERSON>, Edit, Trash2, UserX, UserCheck, Package, MapPin, Calendar, AlertTriangle, CheckCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { AlertDialogWrapper } from '@/components/alert-dialog-wrapper';
import { InventoryItem } from '@/types/inventory';
import {
  useInventoryItem,
  useActivateInventoryItem,
  useDeactivateInventoryItem,
  useHardDeleteInventoryItem,
} from '@/hooks/useInventory';
import { formatCurrency } from '@/lib/utils';
import { format } from 'date-fns';
import { id as localeId } from 'date-fns/locale';
import { StockMovementHistory } from './StockMovementHistory';
import { navigateBackToInventory } from '@/lib/utils/navigation';

interface InventoryDetailClientProps {
  inventoryItem: InventoryItem;
  inventoryItemId: string;
}

export function InventoryDetailClient({ inventoryItem: initialInventoryItem, inventoryItemId }: InventoryDetailClientProps) {
  const router = useRouter();

  // Fetch inventory item data with TanStack Query for real-time updates
  const { data: inventoryItem, isLoading, error } = useInventoryItem(inventoryItemId);

  // Mutations
  const activateInventoryItemMutation = useActivateInventoryItem();
  const deactivateInventoryItemMutation = useDeactivateInventoryItem();
  const hardDeleteInventoryItemMutation = useHardDeleteInventoryItem();

  // Use initial inventory item data while loading or if query fails
  const currentInventoryItem = inventoryItem || initialInventoryItem;

  const handleEdit = () => {
    router.push(`/dashboard/inventory/${currentInventoryItem.id}/edit`);
  };

  const handleActivate = async () => {
    await activateInventoryItemMutation.mutateAsync(currentInventoryItem.id);
  };

  const handleDeactivate = async () => {
    await deactivateInventoryItemMutation.mutateAsync(currentInventoryItem.id);
  };

  const handleHardDelete = async () => {
    await hardDeleteInventoryItemMutation.mutateAsync(currentInventoryItem.id);
    router.push('/dashboard/inventory');
  };

  // Helper functions
  const getStatusBadge = () => {
    if (currentInventoryItem.isActive) {
      return (
        <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
          <CheckCircle className="mr-1 h-3 w-3" />
          Aktif
        </Badge>
      );
    } else {
      return (
        <Badge variant="secondary" className="bg-gray-100 text-gray-800 border-gray-200">
          <UserX className="mr-1 h-3 w-3" />
          Tidak Aktif
        </Badge>
      );
    }
  };

  const getExpiryStatus = () => {
    if (!currentInventoryItem.expiryDate) return null;

    const expiryDate = new Date(currentInventoryItem.expiryDate);
    const today = new Date();
    const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

    if (daysUntilExpiry < 0) {
      return (
        <Badge variant="destructive" className="bg-red-100 text-red-800 border-red-200">
          <AlertTriangle className="mr-1 h-3 w-3" />
          Kedaluwarsa
        </Badge>
      );
    } else if (daysUntilExpiry <= 30) {
      return (
        <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
          <AlertTriangle className="mr-1 h-3 w-3" />
          Akan Kedaluwarsa ({daysUntilExpiry} hari)
        </Badge>
      );
    }

    return null;
  };

  const getStockLevelBadge = () => {
    const quantity = currentInventoryItem.quantityOnHand;

    if (quantity === 0) {
      return (
        <Badge variant="destructive" className="bg-red-100 text-red-800 border-red-200">
          Stok Habis
        </Badge>
      );
    } else if (quantity <= 10) {
      return (
        <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
          Stok Rendah
        </Badge>
      );
    }

    return (
      <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
        Stok Tersedia
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <p className="text-muted-foreground">Gagal memuat data item inventori</p>
          <Button variant="outline" onClick={() => navigateBackToInventory(router)} className="mt-4">
            Kembali
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => navigateBackToInventory(router)}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Kembali
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">
              {currentInventoryItem.product?.name || 'Item Inventori'}
            </h1>
            <p className="text-muted-foreground">
              {currentInventoryItem.product?.code} • Batch: {currentInventoryItem.batchNumber || 'N/A'}
            </p>
          </div>
        </div>

        <div className="flex flex-col gap-2 sm:flex-row">
          <Button variant="outline" onClick={handleEdit}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Button>

          {currentInventoryItem.isActive ? (
            <AlertDialogWrapper
              title="Nonaktifkan Item Inventori"
              description="Apakah Anda yakin ingin menonaktifkan item inventori ini? Item yang dinonaktifkan tidak akan muncul dalam pencarian aktif."
              confirmText="Nonaktifkan"
              cancelText="Batal"
              pendingText="Menonaktifkan..."
              handler={handleDeactivate}
              disabled={deactivateInventoryItemMutation.isPending}
              variant="destructive"
            >
              <Button variant="outline">
                <UserX className="mr-2 h-4 w-4" />
                Nonaktifkan
              </Button>
            </AlertDialogWrapper>
          ) : (
            <>
              <AlertDialogWrapper
                title="Aktifkan Item Inventori"
                description="Apakah Anda yakin ingin mengaktifkan item inventori ini?"
                confirmText="Aktifkan"
                cancelText="Batal"
                pendingText="Mengaktifkan..."
                handler={handleActivate}
                disabled={activateInventoryItemMutation.isPending}
                variant="primary"
              >
                <Button variant="outline">
                  <UserCheck className="mr-2 h-4 w-4" />
                  Aktifkan
                </Button>
              </AlertDialogWrapper>

              <AlertDialogWrapper
                title="Hapus Item Inventori"
                description="Apakah Anda yakin ingin menghapus item inventori ini secara permanen? Tindakan ini tidak dapat dibatalkan."
                confirmText="HAPUS"
                cancelText="Batal"
                pendingText="Menghapus..."
                handler={handleHardDelete}
                disabled={hardDeleteInventoryItemMutation.isPending}
                variant="destructive"
                requireConfirmationText="HAPUS"
                confirmationPlaceholder="Ketik 'HAPUS' untuk mengonfirmasi"
              >
                <Button variant="destructive">
                  <Trash2 className="mr-2 h-4 w-4" />
                  Hapus
                </Button>
              </AlertDialogWrapper>
            </>
          )}
        </div>
      </div>

      {/* Status Badges */}
      <div className="flex flex-wrap gap-2">
        {getStatusBadge()}
        {getStockLevelBadge()}
        {getExpiryStatus()}
      </div>

      <Separator />

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Informasi Dasar
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 sm:grid-cols-2">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Produk</label>
                  <p className="text-sm font-medium">{currentInventoryItem.product?.name}</p>
                  <p className="text-xs text-muted-foreground">{currentInventoryItem.product?.code}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Unit</label>
                  <p className="text-sm font-medium">
                    {currentInventoryItem.unit?.name} ({currentInventoryItem.unit?.abbreviation})
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Nomor Batch</label>
                  <p className="text-sm font-medium font-mono">
                    {currentInventoryItem.batchNumber || 'Tidak ada'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Lokasi Penyimpanan</label>
                  <p className="text-sm font-medium">
                    {currentInventoryItem.location || 'Tidak ditentukan'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Stock Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Informasi Stok
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 sm:grid-cols-3">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Jumlah Stok</label>
                  <p className="text-2xl font-bold">{currentInventoryItem.quantityOnHand}</p>
                  <p className="text-xs text-muted-foreground">{currentInventoryItem.unit?.abbreviation}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Harga Beli</label>
                  <p className="text-lg font-semibold">{formatCurrency(currentInventoryItem.costPrice)}</p>
                  <p className="text-xs text-muted-foreground">per {currentInventoryItem.unit?.abbreviation}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Harga Jual</label>
                  <p className="text-lg font-semibold">
                    {currentInventoryItem.sellingPrice
                      ? formatCurrency(currentInventoryItem.sellingPrice)
                      : 'Tidak ditetapkan'
                    }
                  </p>
                  {currentInventoryItem.sellingPrice && (
                    <p className="text-xs text-muted-foreground">per {currentInventoryItem.unit?.abbreviation}</p>
                  )}
                </div>
              </div>

              <Separator />

              <div className="grid gap-4 sm:grid-cols-2">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Total Nilai Stok</label>
                  <p className="text-xl font-bold text-green-600">
                    {formatCurrency(currentInventoryItem.quantityOnHand * currentInventoryItem.costPrice)}
                  </p>
                </div>
                {currentInventoryItem.sellingPrice && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Potensi Nilai Jual</label>
                    <p className="text-xl font-bold text-blue-600">
                      {formatCurrency(currentInventoryItem.quantityOnHand * currentInventoryItem.sellingPrice)}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Date Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Informasi Tanggal
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 sm:grid-cols-2">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Tanggal Diterima</label>
                  <p className="text-sm font-medium">
                    {format(new Date(currentInventoryItem.receivedDate), 'dd MMMM yyyy', { locale: localeId })}
                  </p>
                </div>
                {currentInventoryItem.expiryDate && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Tanggal Kedaluwarsa</label>
                    <p className="text-sm font-medium">
                      {format(new Date(currentInventoryItem.expiryDate), 'dd MMMM yyyy', { locale: localeId })}
                    </p>
                  </div>
                )}
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Dibuat</label>
                  <p className="text-sm font-medium">
                    {format(new Date(currentInventoryItem.createdAt), 'dd MMMM yyyy HH:mm', { locale: localeId })}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Terakhir Diperbarui</label>
                  <p className="text-sm font-medium">
                    {format(new Date(currentInventoryItem.updatedAt), 'dd MMMM yyyy HH:mm', { locale: localeId })}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Supplier Information */}
          {currentInventoryItem.supplier && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Supplier
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="font-medium">{currentInventoryItem.supplier.name}</p>
                  <p className="text-sm text-muted-foreground">{currentInventoryItem.supplier.code}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Tipe: {currentInventoryItem.supplier.type}</p>
                  {currentInventoryItem.supplier.city && (
                    <p className="text-sm text-muted-foreground">Kota: {currentInventoryItem.supplier.city}</p>
                  )}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => router.push(`/dashboard/suppliers/${currentInventoryItem.supplier?.id}`)}
                >
                  Lihat Detail Supplier
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          {currentInventoryItem.notes && (
            <Card>
              <CardHeader>
                <CardTitle>Catatan</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                  {currentInventoryItem.notes}
                </p>
              </CardContent>
            </Card>
          )}

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Aksi Cepat</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                variant="outline"
                className="w-full"
                onClick={() => router.push(`/dashboard/products/${currentInventoryItem.productId}`)}
              >
                Lihat Detail Produk
              </Button>
              <Button
                variant="outline"
                className="w-full"
                onClick={handleEdit}
              >
                Edit Item Inventori
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Stock Movement History */}
      <StockMovementHistory
        inventoryItemId={currentInventoryItem.id}
        currentStock={currentInventoryItem.quantityOnHand}
        unitName={currentInventoryItem.unit?.name || 'unit'}
        unitAbbreviation={currentInventoryItem.unit?.abbreviation || 'unit'}
      />
    </div>
  );
}
