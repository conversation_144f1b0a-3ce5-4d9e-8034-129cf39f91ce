'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { format } from 'date-fns';
import { id as localeId } from 'date-fns/locale';
import {
  ArrowLeft,
  Download,
  Filter,
  RefreshCw,
  Search,
  Calendar,
  Package,
  User,
  TrendingDown,
  Clock,
  MapPin,
  DollarSign,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Separator } from '@/components/ui/separator';

import { useAllocationHistory } from '@/hooks/useInventory';
import { AllocationHistoryQueryParams } from '@/types/inventory';
import { formatCurrency } from '@/lib/utils';
import { navigateBackToInventory } from '@/lib/utils/navigation';

export function AllocationHistoryClient() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [filters, setFilters] = useState<AllocationHistoryQueryParams>({
    page: 1,
    limit: 20,
    productId: searchParams.get('productId') || undefined,
  });

  const [showFilters, setShowFilters] = useState(false);

  const {
    data: allocationHistory,
    isLoading,
    error,
    refetch,
  } = useAllocationHistory(filters);

  // Update filters when URL params change
  useEffect(() => {
    const productId = searchParams.get('productId');
    if (productId && productId !== filters.productId) {
      setFilters(prev => ({ ...prev, productId, page: 1 }));
    }
  }, [searchParams, filters.productId]);

  const handleFilterChange = (key: keyof AllocationHistoryQueryParams, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1, // Reset to first page when filters change
    }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const clearFilters = () => {
    const clearedFilters: AllocationHistoryQueryParams = {
      page: 1,
      limit: 20,
    };
    setFilters(clearedFilters);
  };

  const getMethodBadge = (method: string) => {
    const isFefo = method.includes('FEFO');
    return (
      <Badge variant={isFefo ? 'default' : 'secondary'}>
        {isFefo ? 'FEFO' : 'FIFO'}
      </Badge>
    );
  };

  if (error) {
    return (
      <div className="w-full min-w-0 max-w-full space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => navigateBackToInventory(router)}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali
          </Button>
          <h1 className="text-2xl font-bold">Riwayat Alokasi Stok</h1>
        </div>

        <Card>
          <CardContent className="flex flex-col items-center justify-center py-8">
            <p className="text-muted-foreground mb-4">Gagal memuat riwayat alokasi</p>
            <Button variant="outline" onClick={() => refetch()}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Coba Lagi
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full min-w-0 max-w-full space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => navigateBackToInventory(router)}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Riwayat Alokasi Stok</h1>
            <p className="text-muted-foreground">
              Riwayat lengkap alokasi stok dengan metode FIFO/FEFO
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filter Riwayat Alokasi</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="method">Metode</Label>
                <Select
                  value={filters.method || ''}
                  onValueChange={(value) => handleFilterChange('method', value || undefined)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Semua metode" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Semua metode</SelectItem>
                    <SelectItem value="FIFO">FIFO</SelectItem>
                    <SelectItem value="FEFO">FEFO</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="startDate">Tanggal Mulai</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={filters.startDate || ''}
                  onChange={(e) => handleFilterChange('startDate', e.target.value || undefined)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="endDate">Tanggal Akhir</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={filters.endDate || ''}
                  onChange={(e) => handleFilterChange('endDate', e.target.value || undefined)}
                />
              </div>

              <div className="space-y-2">
                <Label>&nbsp;</Label>
                <div className="flex gap-2">
                  <Button variant="outline" onClick={clearFilters} className="flex-1">
                    Reset
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Riwayat Alokasi</CardTitle>
            {allocationHistory?.meta && (
              <div className="text-sm text-muted-foreground">
                Menampilkan {((allocationHistory.meta.page - 1) * allocationHistory.meta.limit) + 1} - {Math.min(allocationHistory.meta.page * allocationHistory.meta.limit, allocationHistory.meta.total)} dari {allocationHistory.meta.total} data
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Memuat riwayat alokasi...</p>
            </div>
          ) : !allocationHistory?.data?.length ? (
            <div className="text-center py-8">
              <TrendingDown className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <p className="text-muted-foreground">Belum ada riwayat alokasi</p>
            </div>
          ) : (
            <>
              {/* Mobile View */}
              <div className="block sm:hidden space-y-4">
                {allocationHistory.data.map((movement) => (
                  <Card key={movement.id} className="p-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Package className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium text-sm">
                            {movement.inventoryItem?.product?.name || 'N/A'}
                          </span>
                        </div>
                        {getMethodBadge(movement.notes || '')}
                      </div>

                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="text-muted-foreground">Jumlah:</span>
                          <div className="font-medium">{movement.quantity} {movement.inventoryItem?.unit?.abbreviation || ''}</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Biaya:</span>
                          <div className="font-medium">{formatCurrency(Number(movement.unitPrice || 0))}</div>
                        </div>
                      </div>

                      <div className="text-xs text-muted-foreground">
                        <div className="flex items-center gap-1 mb-1">
                          <Clock className="h-3 w-3" />
                          {format(new Date(movement.movementDate), 'dd MMM yyyy HH:mm', { locale: localeId })}
                        </div>
                        {movement.reason && (
                          <div>Alasan: {movement.reason}</div>
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>

              {/* Desktop View */}
              <div className="hidden sm:block">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Produk</TableHead>
                        <TableHead>Batch</TableHead>
                        <TableHead>Jumlah</TableHead>
                        <TableHead>Metode</TableHead>
                        <TableHead>Biaya</TableHead>
                        <TableHead>Tanggal</TableHead>
                        <TableHead>Alasan</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {allocationHistory.data.map((movement) => (
                        <TableRow key={movement.id}>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="font-medium text-sm">
                                {movement.inventoryItem?.product?.name || 'N/A'}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {movement.inventoryItem?.product?.code || 'N/A'}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {movement.inventoryItem?.batchNumber || (
                                <span className="text-muted-foreground italic">Tidak ada</span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <TrendingDown className="h-4 w-4 text-orange-500" />
                              <span className="font-medium">{movement.quantity}</span>
                              <span className="text-xs text-muted-foreground">
                                {movement.inventoryItem?.unit?.abbreviation || ''}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            {getMethodBadge(movement.notes || '')}
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">
                              {formatCurrency(Number(movement.unitPrice || 0))}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {format(new Date(movement.movementDate), 'dd MMM yyyy HH:mm', { locale: localeId })}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm max-w-xs truncate">
                              {movement.reason || (
                                <span className="text-muted-foreground italic">Tidak ada</span>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>

              {/* Pagination */}
              {allocationHistory.meta && allocationHistory.meta.totalPages > 1 && (
                <div className="flex items-center justify-between mt-6">
                  <div className="text-sm text-muted-foreground">
                    Halaman {allocationHistory.meta.page} dari {allocationHistory.meta.totalPages}
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={!allocationHistory.meta.hasPreviousPage}
                      onClick={() => handlePageChange(allocationHistory.meta.page - 1)}
                    >
                      Sebelumnya
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={!allocationHistory.meta.hasNextPage}
                      onClick={() => handlePageChange(allocationHistory.meta.page + 1)}
                    >
                      Selanjutnya
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
