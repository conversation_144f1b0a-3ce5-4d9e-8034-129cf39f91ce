import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import {
  SaleQueryParams,
  CreateSaleDto,
  UpdateSaleDto,
} from '@/types/sales';
import { salesApi } from '@/lib/api/sales';
import { toast } from 'sonner';

// Query key factory
export const saleKeys = {
  all: ['sales'] as const,
  lists: () => [...saleKeys.all, 'list'] as const,
  list: (params: SaleQueryParams) => [...saleKeys.lists(), params] as const,
  details: () => [...saleKeys.all, 'detail'] as const,
  detail: (id: string) => [...saleKeys.details(), id] as const,
  stats: () => [...saleKeys.all, 'stats'] as const,
  analytics: (params: any) => [...saleKeys.all, 'analytics', params] as const,
};

// Query Hooks

export function useSales(params: SaleQueryParams) {
  return useQuery({
    queryKey: saleKeys.list(params),
    queryFn: () => salesApi.getSales(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    placeholderData: (previousData) => previousData, // Keep previous data while loading
  });
}

export function useSale(id: string) {
  return useQuery({
    queryKey: saleKeys.detail(id),
    queryFn: () => salesApi.getSale(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useSaleStats() {
  return useQuery({
    queryKey: saleKeys.stats(),
    queryFn: () => salesApi.getSaleStats(),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useSalesAnalytics(params: {
  startDate?: string;
  endDate?: string;
  groupBy?: 'day' | 'week' | 'month';
}) {
  return useQuery({
    queryKey: saleKeys.analytics(params),
    queryFn: () => salesApi.getSalesAnalytics(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!(params.startDate && params.endDate),
  });
}

// Invalidation Hook
export function useSalesInvalidate() {
  const queryClient = useQueryClient();

  const invalidateAll = () => {
    queryClient.invalidateQueries({ queryKey: saleKeys.all });
  };

  const invalidateLists = () => {
    queryClient.invalidateQueries({ queryKey: saleKeys.lists() });
  };

  const invalidateStats = () => {
    queryClient.invalidateQueries({ queryKey: saleKeys.stats() });
  };

  const invalidateDetail = (id: string) => {
    queryClient.invalidateQueries({ queryKey: saleKeys.detail(id) });
  };

  return {
    invalidateAll,
    invalidateLists,
    invalidateStats,
    invalidateDetail,
  };
}

// Mutation Hooks

// Sale CRUD Mutations
export function useCreateSale() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateSaleDto) => salesApi.createSale(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: saleKeys.lists() });
      queryClient.invalidateQueries({ queryKey: saleKeys.stats() });
      toast.success(`Transaksi ${data.saleNumber} berhasil dibuat`);
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal membuat transaksi';
      toast.error(message);
    },
  });
}

export function useCreateDraft() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateSaleDto) => salesApi.createDraft(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: saleKeys.lists() });
      queryClient.invalidateQueries({ queryKey: saleKeys.stats() });
      toast.success(`Draft transaksi ${data.saleNumber} berhasil dibuat`);
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal membuat draft transaksi';
      toast.error(message);
    },
  });
}

export function useUpdateSale() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateSaleDto }) => salesApi.updateSale(id, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: saleKeys.lists() });
      queryClient.invalidateQueries({ queryKey: saleKeys.detail(data.id) });
      queryClient.invalidateQueries({ queryKey: saleKeys.stats() });
      toast.success(`Transaksi ${data.saleNumber} berhasil diperbarui`);
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal memperbarui transaksi';
      toast.error(message);
    },
  });
}

export function useUpdateDraft() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateSaleDto }) => salesApi.updateDraft(id, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: saleKeys.lists() });
      queryClient.invalidateQueries({ queryKey: saleKeys.detail(data.id) });
      queryClient.invalidateQueries({ queryKey: saleKeys.stats() });
      toast.success(`Draft transaksi ${data.saleNumber} berhasil diperbarui`);
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal memperbarui draft transaksi';
      toast.error(message);
    },
  });
}

// Sale Status Mutations
export function useCompleteSale() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => salesApi.completeSale(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: saleKeys.lists() });
      queryClient.invalidateQueries({ queryKey: saleKeys.detail(data.id) });
      queryClient.invalidateQueries({ queryKey: saleKeys.stats() });
      toast.success(`Transaksi ${data.saleNumber} berhasil diselesaikan`);
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal menyelesaikan transaksi';
      toast.error(message);
    },
  });
}

export function useCancelSale() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, reason }: { id: string; reason?: string }) => salesApi.cancelSale(id, reason),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: saleKeys.lists() });
      queryClient.invalidateQueries({ queryKey: saleKeys.detail(data.id) });
      queryClient.invalidateQueries({ queryKey: saleKeys.stats() });
      toast.success(`Transaksi ${data.saleNumber} berhasil dibatalkan`);
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal membatalkan transaksi';
      toast.error(message);
    },
  });
}

export function useRefundSale() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, reason }: { id: string; reason?: string }) => salesApi.refundSale(id, reason),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: saleKeys.lists() });
      queryClient.invalidateQueries({ queryKey: saleKeys.detail(data.id) });
      queryClient.invalidateQueries({ queryKey: saleKeys.stats() });
      toast.success(`Transaksi ${data.saleNumber} berhasil dikembalikan`);
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal mengembalikan transaksi';
      toast.error(message);
    },
  });
}

// Receipt Validation Query
export function useValidateReceiptGeneration(saleId: string) {
  return useQuery({
    queryKey: [...saleKeys.detail(saleId), 'receipt-validation'],
    queryFn: () => salesApi.validateReceiptGeneration(saleId),
    enabled: !!saleId,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 60 * 1000, // 1 minute
  });
}

// Receipt Generation Mutations
export function useGenerateReceipt() {
  return useMutation({
    mutationFn: (id: string) => salesApi.generateReceipt(id),
    onSuccess: () => {
      toast.success('Struk berhasil dibuat');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal membuat struk';
      toast.error(message);
    },
  });
}

export function useGenerateReceiptWide() {
  return useMutation({
    mutationFn: (id: string) => salesApi.generateReceiptWide(id),
    onSuccess: () => {
      toast.success('Struk lebar berhasil dibuat');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal membuat struk lebar';
      toast.error(message);
    },
  });
}

// PDF Download Mutations
export function useDownloadReceiptPdf() {
  return useMutation({
    mutationFn: (id: string) => salesApi.downloadReceiptPdf(id),
    onSuccess: () => {
      toast.success('PDF struk berhasil diunduh');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal mengunduh PDF struk';
      toast.error(message);
    },
  });
}

export function useDownloadReceiptWidePdf() {
  return useMutation({
    mutationFn: (id: string) => salesApi.downloadReceiptWidePdf(id),
    onSuccess: () => {
      toast.success('PDF struk lebar berhasil diunduh');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal mengunduh PDF struk lebar';
      toast.error(message);
    },
  });
}

// Export Mutations
export function useExportSales() {
  return useMutation({
    mutationFn: (params: SaleQueryParams & { format?: 'excel' | 'csv' | 'pdf' }) => salesApi.exportSales(params),
    onSuccess: () => {
      toast.success('Data penjualan berhasil diekspor');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal mengekspor data penjualan';
      toast.error(message);
    },
  });
}

// Delete Mutations
export function useDeleteSale() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => salesApi.deleteSale(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: saleKeys.lists() });
      queryClient.invalidateQueries({ queryKey: saleKeys.stats() });
      toast.success('Transaksi berhasil dihapus');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal menghapus transaksi';
      toast.error(message);
    },
  });
}

// Bulk Operations
export function useBulkUpdateSaleStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ saleIds, status, reason }: { saleIds: string[]; status: string; reason?: string }) =>
      salesApi.bulkUpdateSaleStatus(saleIds, status, reason),
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: saleKeys.lists() });
      queryClient.invalidateQueries({ queryKey: saleKeys.stats() });
      toast.success(`${result.updated} transaksi berhasil diperbarui`);
      if (result.failed > 0) {
        toast.warning(`${result.failed} transaksi gagal diperbarui`);
      }
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal memperbarui status transaksi';
      toast.error(message);
    },
  });
}

export function useBulkDeleteSales() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (saleIds: string[]) => salesApi.bulkDeleteSales(saleIds),
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: saleKeys.lists() });
      queryClient.invalidateQueries({ queryKey: saleKeys.stats() });
      toast.success(`${result.deleted} transaksi berhasil dihapus`);
      if (result.failed > 0) {
        toast.warning(`${result.failed} transaksi gagal dihapus`);
      }
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal menghapus transaksi';
      toast.error(message);
    },
  });
}

// Sale number generation hooks
export function useGenerateSaleNumber() {
  return useQuery({
    queryKey: ['sale-number-generation'],
    queryFn: () => salesApi.generateSaleNumber(),
    enabled: false, // Only run when manually triggered
  });
}

export function useValidateSaleNumber(saleNumber: string) {
  return useQuery({
    queryKey: ['sale-number-validation', saleNumber],
    queryFn: () => salesApi.validateSaleNumber(saleNumber),
    enabled: !!saleNumber && saleNumber.length > 0,
    staleTime: 30 * 1000, // 30 seconds
  });
}


