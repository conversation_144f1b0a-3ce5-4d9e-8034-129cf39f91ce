export enum SupplierType {
  PBF = 'PBF',
  MANUFACTURER = 'MANUFACTURER',
  DISTRIBUTOR = 'DISTRIBUTOR',
  LOCAL = 'LOCAL',
}

export enum SupplierStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
  PENDING = 'PENDING',
}

export enum PaymentMethod {
  CASH = 'CASH',
  TRANSFER = 'TRANSFER',
  CREDIT = 'CREDIT',
  GIRO = 'GIRO',
}

export enum DocumentType {
  LICENSE = 'LICENSE',
  CERTIFICATE = 'CERTIFICATE',
  CONTRACT = 'CONTRACT',
  TAX_DOCUMENT = 'TAX_DOCUMENT',
  PAYMENT_PROOF = 'PAYMENT_PROOF',
  OTHER = 'OTHER',
}

export interface SupplierContact {
  id: string;
  supplierId: string;
  name: string;
  position?: string;
  phone?: string;
  email?: string;
  isPrimary: boolean;
  isActive: boolean;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SupplierDocument {
  id: string;
  supplierId: string;
  paymentId?: string; // For PAYMENT_PROOF documents
  type: DocumentType;
  name: string;
  description?: string;
  fileName?: string;
  filePath?: string;
  fileSize?: number;
  mimeType?: string;
  expiryDate?: string;
  isActive: boolean;
  uploadedAt: string;
  uploadedBy?: string;
}

export interface SupplierPayment {
  id: string;
  supplierId: string;
  invoiceNumber?: string;
  amount: number;
  paymentMethod: PaymentMethod;
  paymentDate: string;
  dueDate?: string;
  status: string;
  reference?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
}

export interface Supplier {
  id: string;
  code: string;
  name: string;
  type: SupplierType;
  status: SupplierStatus;
  
  // Contact Information
  address?: string;
  city?: string;
  province?: string;
  postalCode?: string;
  phone?: string;
  email?: string;
  website?: string;
  
  // Business Information
  npwp?: string;
  licenseNumber?: string;
  pharmacyLicense?: string;
  
  // Financial Information
  creditLimit?: number;
  paymentTerms?: number;
  preferredPayment?: PaymentMethod;
  
  // Bank Information
  bankName?: string;
  bankAccount?: string;
  bankAccountName?: string;
  
  // Metadata
  notes?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
  createdByUser?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  updatedByUser?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  
  // Relations
  contacts?: SupplierContact[];
  documents?: SupplierDocument[];
  payments?: SupplierPayment[];
  _count?: {
    documents: number;
    payments: number;
  };
}

export interface CreateSupplierContactDto {
  name: string;
  position?: string;
  phone?: string;
  email?: string;
  isPrimary?: boolean;
  notes?: string;
}

export interface CreateSupplierDto {
  code: string;
  name: string;
  type: SupplierType;
  address?: string;
  city?: string;
  province?: string;
  postalCode?: string;
  phone?: string;
  email?: string;
  website?: string;
  npwp?: string;
  licenseNumber?: string;
  pharmacyLicense?: string;
  creditLimit?: number;
  paymentTerms?: number;
  preferredPayment?: PaymentMethod;
  bankName?: string;
  bankAccount?: string;
  bankAccountName?: string;
  notes?: string;
  contacts?: CreateSupplierContactDto[];
}

export interface UpdateSupplierDto extends Partial<CreateSupplierDto> {
  status?: SupplierStatus;
}

export interface SupplierQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  type?: SupplierType;
  status?: SupplierStatus;
  city?: string;
  province?: string;
  paymentMethod?: PaymentMethod;
  email?: string;
  phone?: string;
  npwp?: string;
  createdBy?: string;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface SupplierListResponse {
  data: Supplier[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface SupplierStats {
  total: number;
  active: number;
  inactive: number;
  byType: Record<SupplierType, number>;
}

export interface CreateSupplierDocumentDto {
  type: DocumentType;
  name: string;
  description?: string;
  fileName?: string;
  filePath?: string;
  fileSize?: number;
  mimeType?: string;
  expiryDate?: string;
}

export interface CreatePaymentProofDto {
  name: string;
  description?: string;
  fileName?: string;
  filePath?: string;
  fileSize?: number;
  mimeType?: string;
}

export interface CreateSupplierPaymentDto {
  invoiceNumber?: string;
  amount: number;
  paymentMethod: PaymentMethod;
  paymentDate: string;
  dueDate?: string;
  status?: string;
  reference?: string;
  notes?: string;
}

// UI Helper Types
export interface SupplierTypeOption {
  value: SupplierType;
  label: string;
}

export interface SupplierStatusOption {
  value: SupplierStatus;
  label: string;
}

export interface PaymentMethodOption {
  value: PaymentMethod;
  label: string;
}

// Document Management Types
export interface DocumentQueryParams {
  page?: number;
  limit?: number;
  type?: DocumentType;
  search?: string;
}

export interface DocumentListResponse {
  data: SupplierDocument[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

// Payment Management Types
export interface PaymentQueryParams {
  page?: number;
  limit?: number;
  status?: string;
  paymentMethod?: PaymentMethod;
  search?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface PaymentListResponse {
  data: SupplierPayment[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface PaymentSummary {
  summary: {
    totalPaid: number;
    totalPending: number;
    totalOverdue: number;
    countPaid: number;
    countPending: number;
    countOverdue: number;
  };
  recentPayments: SupplierPayment[];
  paymentsByMethod: {
    method: PaymentMethod;
    amount: number;
    count: number;
  }[];
  paymentsByMonth: {
    date: string;
    amount: number;
    count: number;
  }[];
}

// Document Type Options
export interface DocumentTypeOption {
  value: DocumentType;
  label: string;
}

// Payment Status Options
export interface PaymentStatusOption {
  value: string;
  label: string;
}
