// Enums from Prisma
export enum ProductType {
  MEDICINE = 'MEDICINE',
  MEDICAL_DEVICE = 'MEDICAL_DEVICE',
  SUPPLEMENT = 'SUPPLEMENT',
  COSMETIC = 'COSMETIC',
  GENERAL = 'GENERAL',
}

export enum ProductCategory {
  // Medicine Categories
  ANALGESIC = 'ANALGESIC', // Obat Pereda Nyeri
  ANTIBIOTIC = 'ANTIBIOTIC', // Antibiotik
  ANTACID = 'ANTACID', // Antasida
  VITAMIN = 'VITAMIN', // Vitamin
  SUPPLEMENT = 'SUPPLEMENT', // Suplemen
  COUGH_COLD = 'COUGH_COLD', // Obat Batuk Pilek
  DIGESTIVE = 'DIGESTIVE', // Obat Pencernaan
  TOPICAL = 'TOPICAL', // Obat Luar
  CARDIOVASCULAR = 'CARDIOVASCULAR', // Obat Jantung
  DIABETES = 'DIABETES', // Obat Diabetes
  HYPERTENSION = 'HYPERTENSION', // Obat Hipertensi

  // Non-Medicine Categories
  MEDICAL_DEVICE = 'MEDICAL_DEVICE', // Alat Kesehatan
  COSMETIC = 'COSMETIC', // Kosmetik
  BABY_CARE = 'BABY_CARE', // Perawatan Bayi
  PERSONAL_CARE = 'PERSONAL_CARE', // Perawatan Pribadi
  FIRST_AID = 'FIRST_AID', // P3K
  OTHER = 'OTHER', // Lainnya
}

export enum MedicineClassification {
  OBAT_BEBAS = 'OBAT_BEBAS', // Green circle - Over the counter
  OBAT_BEBAS_TERBATAS = 'OBAT_BEBAS_TERBATAS', // Blue circle - Limited OTC
  OBAT_KERAS = 'OBAT_KERAS', // Red circle with K - Prescription only
  NARKOTIKA = 'NARKOTIKA', // Red circle with special marking - Narcotics
  PSIKOTROPIKA = 'PSIKOTROPIKA', // Special marking - Psychotropics
  JAMU = 'JAMU', // Traditional medicine
  OBAT_HERBAL_TERSTANDAR = 'OBAT_HERBAL_TERSTANDAR', // Standardized herbal
  FITOFARMAKA = 'FITOFARMAKA', // Phytopharmaceuticals
  NON_MEDICINE = 'NON_MEDICINE', // Not a medicine
}

export enum UnitType {
  WEIGHT = 'WEIGHT', // kg, g, mg
  VOLUME = 'VOLUME', // L, ml
  COUNT = 'COUNT', // pieces, tablets, capsules
  LENGTH = 'LENGTH', // m, cm, mm
  AREA = 'AREA', // m2, cm2
  PACKAGE = 'PACKAGE', // box, strip, bottle
}

// Base interfaces
export interface ProductUnit {
  id: string;
  name: string;
  abbreviation: string;
  type: UnitType;
  isBaseUnit: boolean;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ProductUnitHierarchy {
  id: string;
  productId: string;
  unitId: string;
  parentUnitId?: string;
  conversionFactor: number;
  level: number;
  sellingPrice?: number;
  costPrice?: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  unit: ProductUnit;
  parentUnit?: ProductUnitHierarchy;
  childUnits?: ProductUnitHierarchy[];
}

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

export interface Product {
  id: string;
  code: string;
  name: string;
  genericName?: string;
  type: ProductType;
  category: ProductCategory;
  manufacturer?: string;

  // Indonesian Pharmacy Specific Fields
  bpomNumber?: string;
  medicineClassification: MedicineClassification;
  regulatorySymbol?: string;

  // Unit and Pricing Information
  baseUnitId: string;
  minimumStock?: number;
  maximumStock?: number;
  reorderPoint?: number;

  // Product Information
  description?: string;
  activeIngredient?: string;
  strength?: string;
  dosageForm?: string;
  indication?: string;
  contraindication?: string;
  sideEffects?: string;
  dosage?: string;
  storage?: string;

  // Metadata
  notes?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;

  // Relations
  baseUnit: ProductUnit;
  unitHierarchies: ProductUnitHierarchy[];
  createdByUser?: User;
  updatedByUser?: User;
}

// DTO interfaces
export interface CreateProductUnitHierarchyDto {
  unitId: string;
  parentUnitId?: string;
  conversionFactor: number;
  level: number;
  sellingPrice?: number;
  costPrice?: number;
}

export interface CreateProductDto {
  code: string;
  name: string;
  genericName?: string;
  type: ProductType;
  category: ProductCategory;
  manufacturer?: string;
  bpomNumber?: string;
  medicineClassification: MedicineClassification;
  regulatorySymbol?: string;
  baseUnitId: string;
  minimumStock?: number;
  maximumStock?: number;
  reorderPoint?: number;
  description?: string;
  activeIngredient?: string;
  strength?: string;
  dosageForm?: string;
  indication?: string;
  contraindication?: string;
  sideEffects?: string;
  dosage?: string;
  storage?: string;
  notes?: string;
  unitHierarchies?: CreateProductUnitHierarchyDto[];
}

export interface UpdateProductUnitHierarchyDto extends CreateProductUnitHierarchyDto {
  id?: string;
  isActive?: boolean;
}

export interface UpdateProductDto extends Partial<CreateProductDto> {
  isActive?: boolean;
  unitHierarchies?: UpdateProductUnitHierarchyDto[];
}

export interface ProductQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  type?: ProductType;
  category?: ProductCategory;
  medicineClassification?: MedicineClassification;
  manufacturer?: string;
  isActive?: boolean;
  baseUnitId?: string;
  genericName?: string;
  bpomNumber?: string;
  activeIngredient?: string;
  dosageForm?: string;
  strength?: string;
  createdBy?: string;
  dateFrom?: string;
  dateTo?: string;
  lowStock?: boolean;
  outOfStock?: boolean;
  overStock?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ProductListResponse {
  data: Product[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface ProductStats {
  total: number;
  active: number;
  inactive: number;
  byType: Record<ProductType, number>;
  byCategory: Record<ProductCategory, number>;
  byMedicineClassification: Record<MedicineClassification, number>;
}
