export enum CustomerType {
  WALK_IN = 'WALK_IN',
  REGISTERED = 'REGISTERED',
}

export interface Customer {
  id: string;
  code: string;
  type: CustomerType;
  firstName?: string;
  lastName?: string;
  fullName: string;
  phoneNumber?: string;
  email?: string;
  dateOfBirth?: string;
  gender?: string;
  address?: string;
  city?: string;
  province?: string;
  postalCode?: string;
  membershipNumber?: string;
  membershipLevel?: string;
  loyaltyPoints: number;
  notes?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
}

export interface CreateCustomerDto {
  code?: string;
  type: CustomerType;
  firstName?: string;
  lastName?: string;
  fullName: string;
  phoneNumber?: string;
  email?: string;
  dateOfBirth?: string;
  gender?: string;
  address?: string;
  city?: string;
  province?: string;
  postalCode?: string;
  membershipNumber?: string;
  membershipLevel?: string;
  notes?: string;
}

export interface UpdateCustomerDto extends Partial<CreateCustomerDto> {}

export interface CustomerQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  type?: CustomerType;
  membershipLevel?: string;
  isActive?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface CustomerListResponse {
  data: Customer[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface CustomerStats {
  total: number;
  active: number;
  inactive: number;
  walkIn: number;
  registered: number;
  byMembershipLevel: Array<{
    level: string;
    count: number;
  }>;
}

// Search result interface for customer selector
export interface CustomerSearchResult {
  id: string;
  name: string;
  phone?: string;
  code: string;
  type: CustomerType;
  membershipLevel?: string;
}
