import { Product } from './product';
import { Supplier } from './supplier';
import { ProductUnit } from './product';

export interface InventoryItem {
  id: string;
  productId: string;
  unitId: string;
  batchNumber?: string;
  expiryDate?: string;
  quantityOnHand: number; // Physical stock (actual inventory)
  quantityAllocated: number; // Allocated/reserved stock
  costPrice: number;
  sellingPrice?: number;
  location?: string;
  receivedDate: string;
  supplierId?: string;
  isActive: boolean;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;

  // Relations
  product: Product;
  unit: ProductUnit;
  supplier?: Supplier;
  stockMovements?: StockMovement[];

  // Allocation summary (included in list queries for performance)
  allocationSummary?: AllocationSummary;
}

export interface StockMovement {
  id: string;
  inventoryItemId: string;
  type: StockMovementType;
  quantity: number;
  unitPrice?: number;
  referenceType?: string;
  referenceId?: string;
  referenceNumber?: string;
  reason?: string;
  notes?: string;
  movementDate: string;
  createdAt: string;
  createdBy?: string;

  // Relations
  inventoryItem: InventoryItem;
}

export interface StockMovementResponse {
  data: StockMovement[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

// Allocation tracking types
export interface AllocationSummary {
  totalAllocated: number;
  lastAllocation?: string;
  recentAllocations: number;
}

export interface AllocationHistoryQueryParams {
  page?: number;
  limit?: number;
  productId?: string;
  userId?: string;
  method?: string;
  startDate?: string;
  endDate?: string;
}

export interface AllocationHistoryResponse {
  data: StockMovement[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface StockMovementQueryParams {
  page?: number;
  limit?: number;
  type?: StockMovementType;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: 'createdAt' | 'movementDate' | 'quantity' | 'type';
  sortOrder?: 'asc' | 'desc';
}

export enum StockMovementType {
  IN = 'IN',
  OUT = 'OUT',
  ADJUSTMENT = 'ADJUSTMENT',
  TRANSFER = 'TRANSFER',
  ALLOCATION = 'ALLOCATION',
}

export interface CreateInventoryItemData {
  productId: string;
  unitId: string;
  batchNumber?: string;
  expiryDate?: string;
  quantityOnHand: number;
  costPrice: number;
  sellingPrice?: number;
  location?: string;
  receivedDate?: string;
  supplierId?: string;
  isActive?: boolean;
  notes?: string;
}

export interface UpdateInventoryItemData {
  unitId?: string;
  batchNumber?: string;
  expiryDate?: string;
  quantityOnHand?: number;
  costPrice?: number;
  sellingPrice?: number;
  location?: string;
  receivedDate?: string;
  supplierId?: string;
  isActive?: boolean;
  notes?: string;
}

export enum StockAdjustmentType {
  INCREASE = 'INCREASE',
  DECREASE = 'DECREASE',
}

export interface StockAdjustmentData {
  quantity: number; // Can be positive (increase) or negative (decrease) - matches backend DTO
  reason: string;
  notes?: string;
}

// Predefined adjustment reasons
export const STOCK_ADJUSTMENT_REASONS = [
  'Penyesuaian Stok Fisik',
  'Kerusakan Produk',
  'Produk Kedaluwarsa',
  'Kehilangan/Pencurian',
  'Penerimaan Tambahan',
  'Koreksi Data Entry',
  'Transfer Antar Lokasi',
  'Retur ke Supplier',
  'Sampling/Testing',
  'Lainnya',
] as const;

export type StockAdjustmentReason = typeof STOCK_ADJUSTMENT_REASONS[number];

export interface InventoryQueryParams {
  // Pagination
  page?: number;
  limit?: number;
  cursor?: string;
  paginationType?: 'offset' | 'cursor';

  // Search
  search?: string;
  searchOperator?: 'contains' | 'startsWith' | 'endsWith' | 'equals';

  // Product filters
  productId?: string;
  productIds?: string[];
  productType?: string;
  productCategory?: string;

  // Supplier filters
  supplierId?: string;
  supplierIds?: string[];

  // Location and batch
  location?: string;
  batchNumber?: string;

  // Status filters
  isActive?: boolean;

  // Stock filters
  lowStock?: boolean;
  lowStockThreshold?: number;
  quantityMin?: number;
  quantityMax?: number;

  // Expiry filters
  expiringSoon?: boolean;
  expiringSoonDays?: number;
  expired?: boolean;

  // Price filters
  costPriceMin?: number;
  costPriceMax?: number;
  sellingPriceMin?: number;
  sellingPriceMax?: number;

  // Date filters
  expiryDateFrom?: string;
  expiryDateTo?: string;
  receivedDateFrom?: string;
  receivedDateTo?: string;
  createdDateFrom?: string;
  createdDateTo?: string;

  // Sorting
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  sortFields?: string[];

  // Advanced options
  includeAggregations?: boolean;
  includeMovementStats?: boolean;
}

export interface InventoryStats {
  totalItems: number;
  activeItems: number;
  lowStockItems: number;
  expiredItems: number;
  expiringSoonItems: number;
  totalValue: number;
}

export interface InventoryResponse {
  data: InventoryItem[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

// Stock Consumption Types
export enum StockAllocationMethod {
  FIFO = 'FIFO', // First In First Out
  FEFO = 'FEFO', // First Expired First Out
}

export interface StockConsumptionData {
  productId: string;
  requestedQuantity: number;
  method: StockAllocationMethod;
  userId?: string;
  reason?: string;
  notes?: string;
  allowPartialAllocation?: boolean;
  nearExpiryWarningDays?: number;
  previewOnly?: boolean;
}

export interface StockBatchInfo {
  inventoryItemId: string;
  batchNumber?: string;
  quantityOnHand: number;
  expiryDate?: string;
  receivedDate: string;
  location?: string;
}

export interface StockAvailabilityResult {
  isAvailable: boolean;
  totalAvailable: number;
  requestedQuantity: number;
  shortfall: number;
  batchCount: number;
  batches: StockBatchInfo[];
}

export interface StockConsumptionBatch {
  inventoryItemId: string;
  batchNumber?: string;
  expiryDate?: string;
  receivedDate: string;
  availableQuantity: number;
  allocatedQuantity: number;
  costPrice: number;
  location?: string;
  isNearExpiry?: boolean;
  daysUntilExpiry?: number;
}

export interface StockConsumptionResult {
  success: boolean;
  productId: string;
  requestedQuantity: number;
  allocatedQuantity: number;
  method: StockAllocationMethod;
  batches: StockConsumptionBatch[];
  shortfall?: number;
  warnings: string[];
  errors: string[];
  totalCost: number;
  averageCostPrice: number;
  previewOnly: boolean;
  timestamp: string;
}

export interface StockConsumptionOptions {
  userId?: string;
  reason?: string;
  notes?: string;
  allowPartialAllocation?: boolean;
  nearExpiryWarningDays?: number;
  previewOnly?: boolean;
  respectMinimumStock?: boolean;
  minimumStockLevel?: number;
}
