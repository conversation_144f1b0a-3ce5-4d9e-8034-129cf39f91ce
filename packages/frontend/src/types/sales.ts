export enum SaleStatus {
  DRAFT = 'DRAFT',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED',
}

export enum PaymentMethod {
  CASH = 'CASH',
  TRANSFER = 'TRANSFER',
  CREDIT = 'CREDIT',
  GIRO = 'GIRO',
}

export enum CustomerType {
  WALK_IN = 'WALK_IN',
  REGISTERED = 'REGISTERED',
}

export interface SaleItem {
  id: string;
  saleId: string;
  productId: string;
  unitId: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  discountType?: string;
  discountValue?: number;
  discountAmount: number;
  batchNumber?: string;
  expiryDate?: string;
  notes?: string;
  createdAt: string;

  // Relations
  product: {
    id: string;
    code: string;
    name: string;
    type: string;
    manufacturer?: string;
  };
  unit: {
    id: string;
    name: string;
    abbreviation: string;
  };
}

export interface Sale {
  id: string;
  saleNumber: string;
  customerId?: string;
  cashierId: string;
  status: SaleStatus;
  saleDate: string;
  subtotal: number;
  discountType?: string;
  discountValue?: number;
  discountAmount: number;
  taxAmount: number;
  totalAmount: number;
  paymentMethod: PaymentMethod;
  amountPaid: number;
  changeAmount: number;
  customerName?: string;
  customerPhone?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;

  // Relations
  customer?: {
    id: string;
    code: string;
    fullName: string;
    phoneNumber?: string;
    type: CustomerType;
  };
  cashier: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  saleItems: SaleItem[];
}

export interface CreateSaleItemDto {
  productId: string;
  unitId: string;
  quantity: number;
  unitPrice: number;
  discountType?: string;
  discountValue?: number;
  notes?: string;
}

export interface CreateSaleDto {
  saleNumber?: string;
  customerId?: string;
  cashierId?: string;
  saleDate?: string;
  discountType?: string;
  discountValue?: number;
  taxAmount?: number;
  paymentMethod: PaymentMethod;
  amountPaid: number;
  customerName?: string;
  customerPhone?: string;
  notes?: string;
  items: CreateSaleItemDto[];
}

export interface UpdateSaleDto extends Partial<CreateSaleDto> {
  status?: SaleStatus;
}

export interface SaleQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: SaleStatus;
  paymentMethod?: PaymentMethod;
  customerId?: string;
  cashierId?: string;
  startDate?: string;
  endDate?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  // Additional filters for enhanced functionality
  minAmount?: number;
  maxAmount?: number;
  customerType?: CustomerType;
  includeItems?: boolean;
}

export interface SaleListResponse {
  data: Sale[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface SaleStats {
  totalSales: number;
  todaySales: number;
  completedSales: number;
  draftSales: number;
  cancelledSales: number;
  refundedSales: number;
  totalRevenue: number;
  todayRevenue: number;
  averageOrderValue: number;
  topPaymentMethod: PaymentMethod;
}

// Filter options for the datatable
export interface SaleStatusOption {
  value: SaleStatus;
  label: string;
}

export interface PaymentMethodOption {
  value: PaymentMethod;
  label: string;
}

// Bulk operations
export interface BulkSaleOperation {
  saleIds: string[];
  operation: 'complete' | 'cancel' | 'delete';
  reason?: string;
}

export interface BulkSaleResult {
  success: number;
  failed: number;
  errors: string[];
}

// Enhanced response types
export interface SaleResponse extends Sale {
  // Additional computed fields that might come from the API
  itemCount?: number;
  profitAmount?: number;
  discountPercentage?: number;
}

// Filter state for UI components
export interface SaleFilters {
  status?: SaleStatus | 'all';
  paymentMethod?: PaymentMethod | 'all';
  dateRange?: {
    startDate?: string;
    endDate?: string;
    preset?: string;
  };
  amountRange?: {
    min?: number;
    max?: number;
  };
  customerType?: CustomerType | 'all';
  search?: string;
}

// Column configuration
export interface SaleColumnConfig {
  key: string;
  label: string;
  sortable: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
  sticky?: boolean;
}

// Action handlers interface
export interface SaleActionHandlers {
  onView: (sale: Sale) => void;
  onEdit: (sale: Sale) => void;
  onComplete: (sale: Sale) => void;
  onCancel: (sale: Sale) => void;
  onRefund: (sale: Sale) => void;
  onGenerateReceipt: (sale: Sale) => void;
  onDelete?: (sale: Sale) => void;
  onDuplicate?: (sale: Sale) => void;
}

// Loading states for actions
export interface SaleActionLoadingStates {
  completing?: boolean;
  cancelling?: boolean;
  refunding?: boolean;
  deleting?: boolean;
  generatingReceipt?: boolean;
}
