import { createApiClient } from './api';
import {
  InventoryItem,
  InventoryResponse,
  InventoryStats,
  InventoryQueryParams,
} from '@/types/inventory';

/**
 * Server-side inventory API functions
 */
export class ServerInventoryApi {
  private api;

  constructor(session?: any) {
    this.api = createApiClient(session);
  }

  async getInventoryItems(params?: InventoryQueryParams): Promise<InventoryResponse> {
    const searchParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
    }
    
    const queryString = searchParams.toString();
    const endpoint = queryString ? `/inventory?${queryString}` : '/inventory';
    
    return this.api.get<InventoryResponse>(endpoint);
  }

  async getInventoryStats(): Promise<InventoryStats> {
    return this.api.get<InventoryStats>('/inventory/stats');
  }

  async getInventoryItem(id: string): Promise<InventoryItem> {
    return this.api.get<InventoryItem>(`/inventory/${id}`);
  }
}

/**
 * Create server-side inventory API instance
 */
export function createServerInventoryApi(session?: any) {
  return new ServerInventoryApi(session);
}

// Export convenience functions for direct use
export const inventoryApi = {
  getInventoryItems: async (params?: InventoryQueryParams): Promise<InventoryResponse> => {
    const api = createServerInventoryApi();
    return api.getInventoryItems(params);
  },

  getInventoryStats: async (): Promise<InventoryStats> => {
    const api = createServerInventoryApi();
    return api.getInventoryStats();
  },

  getInventoryItem: async (id: string): Promise<InventoryItem> => {
    const api = createServerInventoryApi();
    return api.getInventoryItem(id);
  },
};
