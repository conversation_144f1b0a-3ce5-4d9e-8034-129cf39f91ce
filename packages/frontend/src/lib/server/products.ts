import { createApiClient } from './api';
import {
  Product,
  ProductQueryParams,
  ProductListResponse,
  ProductStats,
} from '@/types/product';

/**
 * Server-side product API functions
 */
export class ServerProductsApi {
  private api;

  constructor(session?: any) {
    this.api = createApiClient(session);
  }

  async getProducts(params?: ProductQueryParams): Promise<ProductListResponse> {
    const searchParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
    }
    
    const queryString = searchParams.toString();
    const endpoint = queryString ? `/products?${queryString}` : '/products';
    
    return this.api.get<ProductListResponse>(endpoint);
  }

  async getProduct(id: string): Promise<Product> {
    return this.api.get<Product>(`/products/${id}`);
  }

  async getStats(): Promise<ProductStats> {
    return this.api.get<ProductStats>('/products/stats');
  }
}

/**
 * Create server-side products API instance
 */
export function createProductsApi(session?: any) {
  return new ServerProductsApi(session);
}
