import { apiClient } from '../axios';
import {
  Supplier,
  CreateSupplierDto,
  UpdateSupplierDto,
  SupplierQueryParams,
  SupplierListResponse,
  SupplierStats,
  SupplierDocument,
  CreateSupplierDocumentDto,
  CreatePaymentProofDto,
  SupplierPayment,
  CreateSupplierPaymentDto,
  DocumentQueryParams,
  DocumentListResponse,
  PaymentQueryParams,
  PaymentListResponse,
  PaymentSummary,
} from '@/types/supplier';

export const suppliersApi = {
  // Supplier CRUD operations
  async getSuppliers(params?: SupplierQueryParams): Promise<SupplierListResponse> {
    const response = await apiClient.get('/suppliers', { params });
    return response.data;
  },

  async getSupplier(id: string): Promise<Supplier> {
    const response = await apiClient.get(`/suppliers/${id}`);
    return response.data;
  },

  async createSupplier(data: CreateSupplierDto): Promise<Supplier> {
    const response = await apiClient.post('/suppliers', data);
    return response.data;
  },

  async updateSupplier(id: string, data: UpdateSupplierDto): Promise<Supplier> {
    const response = await apiClient.patch(`/suppliers/${id}`, data);
    return response.data;
  },

  async deleteSupplier(id: string): Promise<void> {
    await apiClient.delete(`/suppliers/${id}`);
  },

  async deactivateSupplier(id: string): Promise<Supplier> {
    const response = await apiClient.patch(`/suppliers/${id}/deactivate`);
    return response.data;
  },

  async activateSupplier(id: string): Promise<Supplier> {
    const response = await apiClient.patch(`/suppliers/${id}/activate`);
    return response.data;
  },

  async hardDeleteSupplier(id: string): Promise<void> {
    await apiClient.delete(`/suppliers/${id}`);
  },

  // Statistics
  async getStats(): Promise<SupplierStats> {
    const response = await apiClient.get('/suppliers/stats');
    return response.data;
  },

  // Document management
  async getSupplierDocuments(
    supplierId: string,
    params?: DocumentQueryParams
  ): Promise<DocumentListResponse> {
    const response = await apiClient.get(`/suppliers/${supplierId}/documents`, { params });
    return response.data;
  },

  async getSupplierDocument(supplierId: string, documentId: string): Promise<SupplierDocument> {
    const response = await apiClient.get(`/suppliers/${supplierId}/documents/${documentId}`);
    return response.data;
  },

  async downloadSupplierDocument(supplierId: string, documentId: string): Promise<Blob> {
    const response = await apiClient.get(`/suppliers/${supplierId}/documents/${documentId}/download`, {
      responseType: 'blob',
    });
    return response.data;
  },

  async createSupplierDocument(
    supplierId: string,
    data: CreateSupplierDocumentDto
  ): Promise<SupplierDocument> {
    const response = await apiClient.post(`/suppliers/${supplierId}/documents`, data);
    return response.data;
  },

  async updateSupplierDocument(
    supplierId: string,
    documentId: string,
    data: Partial<CreateSupplierDocumentDto>
  ): Promise<SupplierDocument> {
    const response = await apiClient.patch(`/suppliers/${supplierId}/documents/${documentId}`, data);
    return response.data;
  },

  async deleteSupplierDocument(supplierId: string, documentId: string): Promise<void> {
    await apiClient.delete(`/suppliers/${supplierId}/documents/${documentId}`);
  },

  async deleteMultipleSupplierDocuments(
    supplierId: string,
    documentIds: string[]
  ): Promise<void> {
    await apiClient.delete(`/suppliers/${supplierId}/documents`, {
      data: { documentIds },
    });
  },

  async uploadSupplierDocument(
    supplierId: string,
    file: File,
    metadata: { type: string; name: string; description?: string }
  ): Promise<SupplierDocument> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', metadata.type);
    formData.append('name', metadata.name);
    if (metadata.description) {
      formData.append('description', metadata.description);
    }

    const response = await apiClient.post(`/suppliers/${supplierId}/documents/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Payment management - with backward compatibility
  // Supports both old format: getSupplierPayments(id, page, limit)
  // and new format: getSupplierPayments(id, params)
  async getSupplierPayments(
    supplierId: string,
    pageOrParams?: number | PaymentQueryParams,
    limit?: number
  ): Promise<PaymentListResponse> {
    let params: PaymentQueryParams;

    // Handle backward compatibility: if second parameter is a number, use old format
    if (typeof pageOrParams === 'number') {
      params = {
        page: pageOrParams,
        limit: limit,
      };
    } else {
      // New format: second parameter is PaymentQueryParams object
      params = pageOrParams || {};
    }

    const response = await apiClient.get(`/suppliers/${supplierId}/payments`, { params });
    return response.data;
  },

  async getSupplierPayment(supplierId: string, paymentId: string): Promise<SupplierPayment> {
    const response = await apiClient.get(`/suppliers/${supplierId}/payments/${paymentId}`);
    return response.data;
  },

  async getSupplierPaymentSummary(supplierId: string): Promise<PaymentSummary> {
    const response = await apiClient.get(`/suppliers/${supplierId}/payments/summary`);
    return response.data;
  },

  async createSupplierPayment(
    supplierId: string,
    data: CreateSupplierPaymentDto
  ): Promise<SupplierPayment> {
    const response = await apiClient.post(`/suppliers/${supplierId}/payments`, data);
    return response.data;
  },

  async updateSupplierPayment(
    supplierId: string,
    paymentId: string,
    data: Partial<CreateSupplierPaymentDto>
  ): Promise<SupplierPayment> {
    const response = await apiClient.patch(`/suppliers/${supplierId}/payments/${paymentId}`, data);
    return response.data;
  },

  async deleteSupplierPayment(supplierId: string, paymentId: string): Promise<void> {
    await apiClient.delete(`/suppliers/${supplierId}/payments/${paymentId}`);
  },

  // Payment Proof management
  async getPaymentProof(supplierId: string, paymentId: string): Promise<SupplierDocument | null> {
    try {
      const response = await apiClient.get(`/suppliers/${supplierId}/payments/${paymentId}/proof`);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null;
      }
      throw error;
    }
  },

  async uploadPaymentProof(
    supplierId: string,
    paymentId: string,
    file: File,
    data: { name: string; description?: string }
  ): Promise<SupplierDocument> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('name', data.name);
    if (data.description) {
      formData.append('description', data.description);
    }

    const response = await apiClient.post(
      `/suppliers/${supplierId}/payments/${paymentId}/proof`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  },

  async deletePaymentProof(supplierId: string, paymentId: string): Promise<void> {
    await apiClient.delete(`/suppliers/${supplierId}/payments/${paymentId}/proof`);
  },

  // Supplier code generation
  async generateSupplierCode(type: string): Promise<{ code: string }> {
    const response = await apiClient.get(`/suppliers/generate-code/${type}`);
    return response.data;
  },

  async validateSupplierCode(code: string): Promise<{ isUnique: boolean }> {
    const response = await apiClient.get(`/suppliers/validate-code/${encodeURIComponent(code)}`);
    return response.data;
  },

  // Payment reference number generation
  async generatePaymentReference(): Promise<{ reference: string }> {
    const response = await apiClient.get('/suppliers/payments/generate-reference');
    return response.data;
  },

  async validatePaymentReference(reference: string): Promise<{ isUnique: boolean }> {
    const response = await apiClient.get(`/suppliers/payments/validate-reference/${encodeURIComponent(reference)}`);
    return response.data;
  },

  // Import/Export operations
  async downloadTemplate(): Promise<Blob> {
    const response = await apiClient.get('/suppliers/template', {
      responseType: 'blob',
    });
    return response.data;
  },

  async importSuppliers(file: File): Promise<any> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await apiClient.post('/suppliers/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  async exportSuppliers(params?: {
    format?: 'csv' | 'xlsx';
    search?: string;
    type?: string;
    status?: string;
    province?: string;
    ids?: string[];
  }): Promise<Blob> {
    const response = await apiClient.get('/suppliers/export', {
      params,
      responseType: 'blob',
    });
    return response.data;
  },
};

export default suppliersApi;
