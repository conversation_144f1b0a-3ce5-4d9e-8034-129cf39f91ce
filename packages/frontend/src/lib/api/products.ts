import { apiClient } from '../axios';
import {
  Product,
  CreateProductDto,
  UpdateProductDto,
  ProductQueryParams,
  ProductListResponse,
  ProductStats,
} from '@/types/product';

export const productsApi = {
  // Product CRUD operations
  async getProducts(params?: ProductQueryParams): Promise<ProductListResponse> {
    const response = await apiClient.get('/products', { params });
    return response.data;
  },

  async getProduct(id: string): Promise<Product> {
    const response = await apiClient.get(`/products/${id}`);
    return response.data;
  },

  async createProduct(data: CreateProductDto): Promise<Product> {
    const response = await apiClient.post('/products', data);
    return response.data;
  },

  async updateProduct(id: string, data: UpdateProductDto): Promise<Product> {
    const response = await apiClient.patch(`/products/${id}`, data);
    return response.data;
  },

  async deleteProduct(id: string): Promise<void> {
    await apiClient.delete(`/products/${id}`);
  },

  async deactivateProduct(id: string): Promise<Product> {
    const response = await apiClient.patch(`/products/${id}/deactivate`);
    return response.data;
  },

  async activateProduct(id: string): Promise<Product> {
    const response = await apiClient.patch(`/products/${id}/activate`);
    return response.data;
  },

  async hardDeleteProduct(id: string): Promise<void> {
    await apiClient.delete(`/products/${id}`);
  },

  // Statistics
  async getStats(): Promise<ProductStats> {
    const response = await apiClient.get('/products/stats');
    return response.data;
  },

  // Product code generation
  async generateProductCode(type: string): Promise<{ code: string }> {
    const response = await apiClient.get(`/products/generate-code/${type}`);
    return response.data;
  },

  async validateProductCode(code: string): Promise<{ isUnique: boolean }> {
    const response = await apiClient.get(`/products/validate-code/${encodeURIComponent(code)}`);
    return response.data;
  },

  // Import/Export operations
  async downloadTemplate(): Promise<Blob> {
    const response = await apiClient.get('/products/template', {
      responseType: 'blob',
    });
    return response.data;
  },

  async importProducts(file: File): Promise<any> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await apiClient.post('/products/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  async exportProducts(params: any): Promise<Blob> {
    const response = await apiClient.get('/products/export', {
      params,
      responseType: 'blob',
    });
    return response.data;
  },
};
