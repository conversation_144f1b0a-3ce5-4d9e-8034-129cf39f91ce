import { apiClient } from '../axios';
import {
  InventoryItem,
  InventoryResponse,
  InventoryStats,
  CreateInventoryItemData,
  UpdateInventoryItemData,
  StockAdjustmentData,
  InventoryQueryParams,
  StockMovementResponse,
  StockMovementQueryParams,
  AllocationSummary,
  AllocationHistoryQueryParams,
  AllocationHistoryResponse,
} from '@/types/inventory';

export const inventoryApi = {
  // Get all inventory items with filtering and pagination
  getInventoryItems: async (params?: InventoryQueryParams): Promise<InventoryResponse> => {
    const searchParams = new URLSearchParams();

    if (!params) {
      const response = await apiClient.get('/inventory');
      return response.data;
    }

    // Pagination
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.cursor) searchParams.append('cursor', params.cursor);
    if (params.paginationType) searchParams.append('paginationType', params.paginationType);

    // Search
    if (params.search) searchParams.append('search', params.search);
    if (params.searchOperator) searchParams.append('searchOperator', params.searchOperator);

    // Product filters
    if (params.productId) searchParams.append('productId', params.productId);
    if (params.productIds?.length) {
      params.productIds.forEach(id => searchParams.append('productIds', id));
    }
    if (params.productType) searchParams.append('productType', params.productType);
    if (params.productCategory) searchParams.append('productCategory', params.productCategory);

    // Supplier filters
    if (params.supplierId) searchParams.append('supplierId', params.supplierId);
    if (params.supplierIds?.length) {
      params.supplierIds.forEach(id => searchParams.append('supplierIds', id));
    }

    // Location and batch
    if (params.location) searchParams.append('location', params.location);
    if (params.batchNumber) searchParams.append('batchNumber', params.batchNumber);

    // Status filters
    if (params.isActive !== undefined) searchParams.append('isActive', params.isActive.toString());

    // Stock filters
    if (params.lowStock !== undefined) searchParams.append('lowStock', params.lowStock.toString());
    if (params.lowStockThreshold !== undefined) searchParams.append('lowStockThreshold', params.lowStockThreshold.toString());
    if (params.quantityMin !== undefined) searchParams.append('quantityMin', params.quantityMin.toString());
    if (params.quantityMax !== undefined) searchParams.append('quantityMax', params.quantityMax.toString());

    // Expiry filters
    if (params.expiringSoon !== undefined) searchParams.append('expiringSoon', params.expiringSoon.toString());
    if (params.expiringSoonDays !== undefined) searchParams.append('expiringSoonDays', params.expiringSoonDays.toString());
    if (params.expired !== undefined) searchParams.append('expired', params.expired.toString());

    // Price filters
    if (params.costPriceMin !== undefined) searchParams.append('costPriceMin', params.costPriceMin.toString());
    if (params.costPriceMax !== undefined) searchParams.append('costPriceMax', params.costPriceMax.toString());
    if (params.sellingPriceMin !== undefined) searchParams.append('sellingPriceMin', params.sellingPriceMin.toString());
    if (params.sellingPriceMax !== undefined) searchParams.append('sellingPriceMax', params.sellingPriceMax.toString());

    // Date filters
    if (params.expiryDateFrom) searchParams.append('expiryDateFrom', params.expiryDateFrom);
    if (params.expiryDateTo) searchParams.append('expiryDateTo', params.expiryDateTo);
    if (params.receivedDateFrom) searchParams.append('receivedDateFrom', params.receivedDateFrom);
    if (params.receivedDateTo) searchParams.append('receivedDateTo', params.receivedDateTo);
    if (params.createdDateFrom) searchParams.append('createdDateFrom', params.createdDateFrom);
    if (params.createdDateTo) searchParams.append('createdDateTo', params.createdDateTo);

    // Sorting
    if (params.sortBy) searchParams.append('sortBy', params.sortBy);
    if (params.sortOrder) searchParams.append('sortOrder', params.sortOrder);
    if (params.sortFields?.length) {
      params.sortFields.forEach(field => searchParams.append('sortFields', field));
    }

    // Advanced options
    if (params.includeAggregations !== undefined) searchParams.append('includeAggregations', params.includeAggregations.toString());
    if (params.includeMovementStats !== undefined) searchParams.append('includeMovementStats', params.includeMovementStats.toString());

    const queryString = searchParams.toString();
    const url = queryString ? `/inventory?${queryString}` : '/inventory';

    const response = await apiClient.get(url);
    return response.data;
  },

  // Get inventory statistics
  getInventoryStats: async (): Promise<InventoryStats> => {
    const response = await apiClient.get('/inventory/stats');
    return response.data;
  },

  // Get single inventory item by ID
  getInventoryItem: async (id: string): Promise<InventoryItem> => {
    const response = await apiClient.get(`/inventory/${id}`);
    return response.data;
  },

  // Create new inventory item
  createInventoryItem: async (data: CreateInventoryItemData): Promise<InventoryItem> => {
    const response = await apiClient.post('/inventory', data);
    return response.data;
  },

  // Update inventory item
  updateInventoryItem: async (id: string, data: UpdateInventoryItemData): Promise<InventoryItem> => {
    const response = await apiClient.patch(`/inventory/${id}`, data);
    return response.data;
  },

  // Adjust stock levels
  adjustStock: async (id: string, data: StockAdjustmentData): Promise<InventoryItem> => {
    const response = await apiClient.patch(`/inventory/${id}/adjust-stock`, data);
    return response.data;
  },

  // Activate inventory item
  activateInventoryItem: async (id: string): Promise<InventoryItem> => {
    const response = await apiClient.patch(`/inventory/${id}/activate`, {
      reason: 'Item inventori diaktifkan melalui interface',
      notes: 'Diaktifkan oleh pengguna'
    });
    return response.data;
  },

  // Deactivate inventory item
  deactivateInventoryItem: async (id: string): Promise<InventoryItem> => {
    const response = await apiClient.patch(`/inventory/${id}/deactivate`, {
      reason: 'Item inventori dinonaktifkan melalui interface',
      notes: 'Dinonaktifkan oleh pengguna'
    });
    return response.data;
  },

  // Hard delete inventory item (permanent removal)
  hardDeleteInventoryItem: async (id: string): Promise<void> => {
    await apiClient.delete(`/inventory/${id}`);
  },

  // Soft delete inventory item (for backward compatibility)
  deleteInventoryItem: async (id: string): Promise<void> => {
    await apiClient.delete(`/inventory/${id}`);
  },

  // Get stock movements for an inventory item
  getStockMovements: async (id: string, params?: StockMovementQueryParams): Promise<StockMovementResponse> => {
    const searchParams = new URLSearchParams();

    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.type) searchParams.append('type', params.type);
    if (params?.dateFrom) searchParams.append('dateFrom', params.dateFrom);
    if (params?.dateTo) searchParams.append('dateTo', params.dateTo);
    if (params?.sortBy) searchParams.append('sortBy', params.sortBy);
    if (params?.sortOrder) searchParams.append('sortOrder', params.sortOrder);

    const queryString = searchParams.toString();
    const url = queryString ? `/inventory/${id}/stock-movements?${queryString}` : `/inventory/${id}/stock-movements`;

    const response = await apiClient.get(url);
    return response.data;
  },

  // Allocation tracking methods
  getAllocationHistory: async (params?: AllocationHistoryQueryParams): Promise<AllocationHistoryResponse> => {
    const searchParams = new URLSearchParams();

    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.productId) searchParams.append('productId', params.productId);
    if (params?.userId) searchParams.append('userId', params.userId);
    if (params?.method) searchParams.append('method', params.method);
    if (params?.startDate) searchParams.append('startDate', params.startDate);
    if (params?.endDate) searchParams.append('endDate', params.endDate);

    const response = await apiClient.get(`/inventory/allocation-history?${searchParams.toString()}`);
    return response.data;
  },

  getProductAllocationSummary: async (productId: string): Promise<AllocationSummary> => {
    const response = await apiClient.get(`/inventory/products/${productId}/allocation-summary`);
    return response.data;
  },
};
