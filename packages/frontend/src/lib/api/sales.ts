import { apiClient } from '../axios';
import {
  Sale,
  CreateSaleDto,
  UpdateSaleDto,
  SaleQueryParams,
  SaleListResponse,
  SaleStats,
} from '@/types/sales';

export const salesApi = {
  // Sale CRUD operations
  async getSales(params?: SaleQueryParams): Promise<SaleListResponse> {
    const searchParams = new URLSearchParams();

    if (!params) {
      const response = await apiClient.get('/sales');
      return response.data;
    }

    // Pagination
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());

    // Search
    if (params.search) searchParams.append('search', params.search);

    // Filters
    if (params.status) searchParams.append('status', params.status);
    if (params.paymentMethod) searchParams.append('paymentMethod', params.paymentMethod);
    if (params.customerId) searchParams.append('customerId', params.customerId);
    if (params.cashierId) searchParams.append('cashierId', params.cashierId);
    if (params.startDate) searchParams.append('startDate', params.startDate);
    if (params.endDate) searchParams.append('endDate', params.endDate);
    if (params.minAmount !== undefined) searchParams.append('minAmount', params.minAmount.toString());
    if (params.maxAmount !== undefined) searchParams.append('maxAmount', params.maxAmount.toString());
    if (params.customerType) searchParams.append('customerType', params.customerType);

    // Sorting
    if (params.sortBy) searchParams.append('sortBy', params.sortBy);
    if (params.sortOrder) searchParams.append('sortOrder', params.sortOrder);

    // Additional options
    if (params.includeItems !== undefined) searchParams.append('includeItems', params.includeItems.toString());

    const queryString = searchParams.toString();
    const url = queryString ? `/sales?${queryString}` : '/sales';

    const response = await apiClient.get(url);
    return response.data;
  },

  async getSale(id: string): Promise<Sale> {
    const response = await apiClient.get(`/sales/${id}`);
    return response.data;
  },

  async createSale(data: CreateSaleDto): Promise<Sale> {
    const response = await apiClient.post('/sales', data);
    return response.data;
  },

  async updateSale(id: string, data: UpdateSaleDto): Promise<Sale> {
    const response = await apiClient.patch(`/sales/${id}`, data);
    return response.data;
  },

  async createDraft(data: CreateSaleDto): Promise<Sale> {
    const response = await apiClient.post('/sales/draft', data);
    return response.data;
  },

  async updateDraft(id: string, data: UpdateSaleDto): Promise<Sale> {
    const response = await apiClient.patch(`/sales/${id}`, data);
    return response.data;
  },

  // Delete sale (soft delete for drafts, hard delete for cancelled)
  async deleteSale(id: string): Promise<void> {
    await apiClient.delete(`/sales/${id}`);
  },

  // Sale status operations
  async completeSale(id: string): Promise<Sale> {
    const response = await apiClient.patch(`/sales/${id}/complete`);
    return response.data;
  },

  async cancelSale(id: string, reason?: string): Promise<Sale> {
    const response = await apiClient.patch(`/sales/${id}/cancel`, { reason });
    return response.data;
  },

  async refundSale(id: string, reason?: string): Promise<Sale> {
    const response = await apiClient.patch(`/sales/${id}/refund`, { reason });
    return response.data;
  },

  // Statistics
  async getSaleStats(): Promise<SaleStats> {
    const response = await apiClient.get('/sales/stats');
    return response.data;
  },

  // Sale number generation
  async generateSaleNumber(): Promise<{ saleNumber: string }> {
    const response = await apiClient.get('/sales/generate-number');
    return response.data;
  },

  async validateSaleNumber(number: string): Promise<{ isUnique: boolean }> {
    const response = await apiClient.get(`/sales/validate-number/${encodeURIComponent(number)}`);
    return response.data;
  },

  // Receipt operations
  async validateReceiptGeneration(id: string): Promise<{ canGenerate: boolean; reason?: string }> {
    const response = await apiClient.get(`/sales/${id}/receipt/validate`);
    return response.data;
  },

  async generateReceipt(id: string): Promise<any> {
    const response = await apiClient.get(`/sales/${id}/receipt`);
    return response.data;
  },

  async generateReceiptWide(id: string): Promise<any> {
    const response = await apiClient.get(`/sales/${id}/receipt/wide`);
    return response.data;
  },

  async downloadReceiptPdf(id: string): Promise<Blob> {
    const response = await apiClient.get(`/sales/${id}/receipt/pdf`, {
      responseType: 'blob',
    });
    return response.data;
  },

  async downloadReceiptWidePdf(id: string): Promise<Blob> {
    const response = await apiClient.get(`/sales/${id}/receipt/wide/pdf`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Export operations
  async exportSales(params?: SaleQueryParams & { format?: 'excel' | 'csv' | 'pdf' }): Promise<Blob> {
    const response = await apiClient.get('/sales/export', {
      params,
      responseType: 'blob',
    });
    return response.data;
  },

  async downloadSalesTemplate(): Promise<Blob> {
    const response = await apiClient.get('/sales/template', {
      responseType: 'blob',
    });
    return response.data;
  },

  // Bulk operations
  async bulkUpdateSaleStatus(
    saleIds: string[],
    status: string,
    reason?: string
  ): Promise<{ updated: number; failed: number; errors: string[] }> {
    const response = await apiClient.patch('/sales/bulk-status', {
      saleIds,
      status,
      reason,
    });
    return response.data;
  },

  async bulkDeleteSales(saleIds: string[]): Promise<{ deleted: number; failed: number; errors: string[] }> {
    const response = await apiClient.delete('/sales/bulk-delete', {
      data: { saleIds },
    });
    return response.data;
  },

  // Analytics and reporting
  async getSalesAnalytics(params: {
    startDate?: string;
    endDate?: string;
    groupBy?: 'day' | 'week' | 'month';
  }): Promise<{
    salesByPeriod: Array<{ period: string; sales: number; revenue: number }>;
    salesByPaymentMethod: Array<{ method: string; sales: number; revenue: number }>;
    salesByStatus: Array<{ status: string; count: number }>;
    topProducts: Array<{ productId: string; productName: string; quantity: number; revenue: number }>;
    topCustomers: Array<{ customerId?: string; customerName: string; sales: number; revenue: number }>;
  }> {
    const response = await apiClient.get('/sales/analytics', { params });
    return response.data;
  },

  // Search and filtering helpers
  async searchCustomers(query: string): Promise<Array<{ id: string; name: string; phone?: string }>> {
    const response = await apiClient.get('/sales/search/customers', {
      params: { q: query },
    });
    return response.data;
  },

  async searchCashiers(query: string): Promise<Array<{ id: string; name: string; email: string }>> {
    const response = await apiClient.get('/sales/search/cashiers', {
      params: { q: query },
    });
    return response.data;
  },

  // Sale number generation
  async generateSaleNumber(): Promise<{ saleNumber: string }> {
    const response = await apiClient.get('/sales/generate-number');
    return response.data;
  },

  async validateSaleNumber(saleNumber: string): Promise<{ isUnique: boolean }> {
    const response = await apiClient.get(`/sales/validate-number/${saleNumber}`);
    return response.data;
  },
};
