import { requireAuth } from '@/lib/server/auth';
import { AllocationHistoryClient } from '@/components/inventory/AllocationHistoryClient';

export default async function AllocationHistoryPage() {
  // Require authentication on server side
  await requireAuth();

  return <AllocationHistoryClient />;
}

export const metadata = {
  title: 'Riwayat Alokasi Stok - Sistem Manajemen Apotek',
  description: 'Riwayat lengkap alokasi stok dengan metode FIFO/FEFO',
};
