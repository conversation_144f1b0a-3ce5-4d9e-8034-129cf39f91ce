import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export default function InventoryLoading() {
  return (
    <div className="w-full min-w-0 max-w-full space-y-6 overflow-hidden">
      {/* Stats Cards Loading */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-1" />
              <Skeleton className="h-3 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Header Loading */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="space-y-2">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-4 w-64" />
        </div>
        <div className="flex flex-col gap-2 sm:flex-row">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-32" />
        </div>
      </div>

      {/* Data Table Loading */}
      <Card className="w-full min-w-0 max-w-full overflow-hidden">
        <CardContent className="p-0 sm:p-6">
          <div className="w-full min-w-0 max-w-full space-y-4">
            {/* Toolbar Loading */}
            <div className="flex flex-col gap-3 lg:flex-row lg:items-center lg:gap-4">
              <Skeleton className="h-9 w-64" />
              <div className="flex flex-wrap items-center gap-2">
                <Skeleton className="h-9 w-32" />
                <Skeleton className="h-9 w-40" />
                <Skeleton className="h-9 w-24" />
                <Skeleton className="h-9 w-20" />
              </div>
            </div>

            {/* Table Loading */}
            <div className="w-full overflow-hidden rounded-md border">
              <div className="bg-blue-50 dark:bg-blue-950/30 px-4 py-3 border-b">
                <Skeleton className="h-4 w-64 mx-auto" />
                <Skeleton className="h-3 w-80 mx-auto mt-1" />
              </div>
              
              <div className="w-full overflow-x-auto">
                <div className="min-w-[1400px]">
                  {/* Table Header */}
                  <div className="border-b bg-muted/50">
                    <div className="flex">
                      {Array.from({ length: 11 }).map((_, i) => (
                        <div key={i} className="px-4 py-3 flex-1">
                          <Skeleton className="h-4 w-20" />
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {/* Table Rows */}
                  {Array.from({ length: 10 }).map((_, i) => (
                    <div key={i} className="border-b">
                      <div className="flex">
                        {Array.from({ length: 11 }).map((_, j) => (
                          <div key={j} className="px-4 py-3 flex-1">
                            <Skeleton className="h-4 w-full" />
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Pagination Loading */}
            <div className="flex items-center justify-between space-x-2 py-4">
              <div className="flex items-center space-x-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-8 w-16" />
              </div>
              <div className="flex items-center space-x-6">
                <Skeleton className="h-4 w-24" />
                <div className="flex items-center space-x-2">
                  <Skeleton className="h-8 w-8" />
                  <Skeleton className="h-8 w-8" />
                  <Skeleton className="h-8 w-8" />
                  <Skeleton className="h-8 w-8" />
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
