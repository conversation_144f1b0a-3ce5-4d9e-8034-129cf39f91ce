import { requireAuth } from '@/lib/server/auth';
import { InventoryReportsClient } from '@/components/inventory/InventoryReportsClient';

export default async function InventoryReportsPage() {
  // Require authentication on server side
  await requireAuth();

  return (
    <div className="w-full min-w-0 max-w-full space-y-6 overflow-hidden">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Laporan Inventori</h1>
          <p className="text-muted-foreground">
            Buat dan kelola laporan inventori dengan berbagai format dan filter
          </p>
        </div>
      </div>

      {/* Client-side interactive components */}
      <InventoryReportsClient />
    </div>
  );
}
