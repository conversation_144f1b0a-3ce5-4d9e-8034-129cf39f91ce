import { requireAuth } from '@/lib/server/auth';
import { StockAllocationClient } from '@/components/inventory/StockAllocationClient';

export default async function StockAllocationPage() {
  // Require authentication on server side
  await requireAuth();

  return <StockAllocationClient />;
}

export const metadata = {
  title: 'Alokasi Stok - Sistem Manajemen Apotek',
  description: 'Interface untuk alokasi stok menggunakan metode FIFO/FEFO',
};
