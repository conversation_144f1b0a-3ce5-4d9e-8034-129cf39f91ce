import { requireAuth } from '@/lib/server/auth';
import { InventoryFormClient } from '@/components/inventory/InventoryFormClient';

export default async function CreateInventoryItemPage() {
  // Require authentication on server side
  await requireAuth();

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Tambah Item Inventori Baru</h1>
        <p className="text-muted-foreground">
          Tambahkan item inventori baru dengan informasi batch, harga, dan lokasi penyimpanan
        </p>
      </div>

      <InventoryFormClient />
    </div>
  );
}
