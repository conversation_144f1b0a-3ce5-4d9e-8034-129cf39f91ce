import { requireAuth } from '@/lib/server/auth';
import { createServerInventoryApi } from '@/lib/server/inventory';
import { InventoryFormClient } from '@/components/inventory/InventoryFormClient';
import { notFound } from 'next/navigation';

interface EditInventoryPageProps {
  params: Promise<{ id: string }>;
}

export default async function EditInventoryPage({ params }: EditInventoryPageProps) {
  // Require authentication on server side
  const session = await requireAuth();

  // Await params before accessing properties (Next.js 15+ requirement)
  const resolvedParams = await params;
  const { id } = resolvedParams;

  try {
    // Fetch inventory item data on server side
    const inventoryApi = createServerInventoryApi(session);
    const inventoryItem = await inventoryApi.getInventoryItem(id);

    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Edit Item Inventori</h1>
          <p className="text-muted-foreground">
            Edit informasi item inventori untuk {inventoryItem.product?.name || 'produk'}
          </p>
        </div>

        <InventoryFormClient inventoryItem={inventoryItem} mode="edit" />
      </div>
    );
  } catch (error: any) {
    if (error?.message?.includes('404') || error?.response?.status === 404) {
      notFound();
    }
    throw error;
  }
}
