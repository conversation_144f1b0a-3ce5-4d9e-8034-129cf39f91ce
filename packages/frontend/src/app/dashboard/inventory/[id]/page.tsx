import { requireAuth } from '@/lib/server/auth';
import { createServerInventoryApi } from '@/lib/server/inventory';
import { InventoryDetailClient } from '@/components/inventory/InventoryDetailClient';
import { notFound } from 'next/navigation';

interface InventoryDetailPageProps {
  params: Promise<{ id: string }>;
}

export default async function InventoryDetailPage({ params }: InventoryDetailPageProps) {
  // Require authentication on server side
  const session = await requireAuth();

  // Await params before accessing properties (Next.js 15+ requirement)
  const resolvedParams = await params;
  const { id } = resolvedParams;

  try {
    // Fetch inventory item data on server side
    const inventoryApi = createServerInventoryApi(session);
    const inventoryItem = await inventoryApi.getInventoryItem(id);

    return <InventoryDetailClient inventoryItem={inventoryItem} inventoryItemId={id} />;
  } catch (error: any) {
    if (error?.response?.status === 404 || error?.message?.includes('404')) {
      notFound();
    }
    throw error;
  }
}
