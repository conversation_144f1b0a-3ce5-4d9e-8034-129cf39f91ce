'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { AppSidebar } from '@/components/app-sidebar';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import { Skeleton } from '@/components/ui/skeleton';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/auth/login');
    }
  }, [session, status, router]);

  if (status === 'loading') {
    return (
      <SidebarProvider
        style={
          {
            "--sidebar-width": "calc(var(--spacing) * 72)",
            "--header-height": "calc(var(--spacing) * 12)",
          } as React.CSSProperties
        }
      >
        {/* Sidebar skeleton */}
        <div className="bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col border-r">
          <div className="flex flex-col flex-grow pt-5 pb-4 overflow-y-auto">
            <div className="flex items-center flex-shrink-0 px-4">
              <Skeleton className="h-8 w-32" />
            </div>
            <div className="mt-8 flex-grow flex flex-col">
              <nav className="flex-1 px-2 space-y-1">
                {Array.from({ length: 8 }).map((_, i) => (
                  <Skeleton key={i} className="h-10 w-full" />
                ))}
              </nav>
            </div>
          </div>
        </div>

        {/* Main content skeleton */}
        <SidebarInset className="flex min-h-svh flex-col">
          {/* Header skeleton */}
          <div className="sticky top-0 z-50 flex h-(--header-height) shrink-0 items-center gap-2 border-b bg-background">
            <Skeleton className="h-full w-full" />
          </div>
          {/* Content skeleton */}
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              {/* Stats cards skeleton */}
              <div className="grid grid-cols-1 gap-4 px-4 lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-4">
                {Array.from({ length: 4 }).map((_, i) => (
                  <Skeleton key={i} className="h-32 w-full" />
                ))}
              </div>
              {/* Chart skeleton */}
              <div className="px-4 lg:px-6">
                <Skeleton className="h-64 w-full" />
              </div>
              {/* Table skeleton */}
              <div className="px-4 lg:px-6">
                <Skeleton className="h-96 w-full" />
              </div>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebar variant="inset" user={session.user} />
      <SidebarInset style={
        {
          "--content-height": "calc(100svh - var(--header-height) - (var(--spacing) * 4))",
        } as React.CSSProperties
      }>
        {children}
      </SidebarInset>
    </SidebarProvider>
  );
}
