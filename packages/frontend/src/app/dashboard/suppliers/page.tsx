import { requireAuth } from '@/lib/server/auth';
import { SuppliersPageClient } from '@/components/suppliers/SuppliersPageClient';
import { SupplierStatsCards } from '@/components/suppliers/SupplierStatsCards';
import { SupplierQueryParams } from '@/types/supplier';


interface SuppliersPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}



export default async function SuppliersPage({ searchParams }: SuppliersPageProps) {
  // Require authentication on server side
  const session = await requireAuth();

  // Await searchParams before accessing properties (Next.js 15+ requirement)
  const resolvedSearchParams = await searchParams;

  // Parse search parameters for filtering
  const filters: SupplierQueryParams = {
    search: typeof resolvedSearchParams.search === 'string' ? resolvedSearchParams.search : undefined,
    type: typeof resolvedSearchParams.type === 'string' ? resolvedSearchParams.type as any : undefined,
    status: typeof resolvedSearchParams.status === 'string' ? resolvedSearchParams.status as any : undefined,
    province: typeof resolvedSearchParams.province === 'string' ? resolvedSearchParams.province : undefined,
    page: typeof resolvedSearchParams.page === 'string' ? parseInt(resolvedSearchParams.page) : 1,
    limit: typeof resolvedSearchParams.limit === 'string' ? parseInt(resolvedSearchParams.limit) : 10,
  };





  return (
    <div className="w-full min-w-0 max-w-full space-y-6 overflow-hidden">
      {/* Stats Cards */}
      <SupplierStatsCards />

      {/* Client-side interactive components */}
      <SuppliersPageClient
        initialQuery={filters}
      />
    </div>
  );
}
