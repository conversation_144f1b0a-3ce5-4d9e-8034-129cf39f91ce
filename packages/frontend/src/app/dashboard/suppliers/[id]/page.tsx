import { notFound } from 'next/navigation';
import { revalidatePath } from 'next/cache';
import {
  Phone,
  Mail,
  Globe,
  MapPin,
  FileText,
  DollarSign,
  Calendar,
  User,
  AlertCircle,
  Clock,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { requireAuth } from '@/lib/server/auth';
import { createSuppliersApi } from '@/lib/server/suppliers';
import { SupplierDetailClient } from '@/components/suppliers/SupplierDetailClient';

import {
  getSupplierTypeLabel,
  getSupplierStatusLabel,
  getSupplierStatusColor,
  getSupplierTypeColor,
  getPaymentMethodLabel,
} from '@/lib/constants/supplier';
import { CopyCodeButton } from '@/components/suppliers/copy-code';
import { formatCurrency } from '@/lib/utils';

interface SupplierDetailPageProps {
  params: Promise<{ id: string }>;
}

async function getSupplierData(session: any, supplierId: string) {
  try {
    const suppliersApi = createSuppliersApi(session);
    const [supplier, paymentSummary] = await Promise.all([
      suppliersApi.getSupplier(supplierId),
      suppliersApi.getSupplierPaymentSummary(supplierId).catch(() => null), // Don't fail if payments can't be loaded
    ]);
    return { supplier, paymentSummary, error: null };
  } catch (error: any) {
    console.error('Error fetching supplier:', error);

    if (error.message?.includes('404')) {
      return { supplier: null, paymentSummary: null, error: 'not_found' };
    }

    let errorMessage = 'Gagal memuat data supplier. Silakan coba lagi.';
    if (error.message?.includes('401')) {
      errorMessage = 'Sesi Anda telah berakhir. Silakan login kembali.';
    } else if (error.message?.includes('403')) {
      errorMessage = 'Anda tidak memiliki akses untuk melihat data supplier.';
    }

    return { supplier: null, paymentSummary: null, error: errorMessage };
  }
}

export default async function SupplierDetailPage({ params }: SupplierDetailPageProps) {
  // Require authentication on server side
  const session = await requireAuth();
  const supplierParam = await params;
  const supplierId = supplierParam.id;

  // Fetch supplier data on server side
  const { supplier, paymentSummary, error } = await getSupplierData(session, supplierId);

  // Handle not found
  if (error === 'not_found') {
    notFound();
  }

  // Server action for refreshing data
  async function refreshData() {
    'use server';
    revalidatePath(`/dashboard/suppliers/${supplierId}`);
  }

  // Handle error state
  if (error && supplier === null) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Terjadi Kesalahan</h1>
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
        <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
              <div className="flex-1">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                  Gagal Memuat Data
                </h3>
                <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                  {error}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // This should not happen due to notFound() call above, but TypeScript safety
  if (!supplier) {
    return null;
  }

  // Calculate outstanding balance from payment summary
  const totalOutstanding = paymentSummary
    ? (paymentSummary.summary.totalPending || 0) + (paymentSummary.summary.totalOverdue || 0)
    : 0;

  const primaryContact = supplier.contacts?.find(contact => contact.isPrimary) || supplier.contacts?.[0];

  return (
    <div className="space-y-6">
      {/* Enhanced Header */}
      <div className="flex flex-col gap-4 lg:flex-row lg:items-start lg:justify-between">
        <div className="flex items-start gap-4">
          <div className="flex-1 min-w-0">
            <div className="flex flex-col sm:flex-row sm:items-center gap-3 mb-2">
              <h1 className="text-2xl sm:text-3xl font-bold tracking-tight break-words">
                {supplier.name}
              </h1>
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline" className={getSupplierStatusColor(supplier.status)}>
                  {getSupplierStatusLabel(supplier.status)}
                </Badge>
                <Badge variant="outline" className={getSupplierTypeColor(supplier.type)}>
                  {getSupplierTypeLabel(supplier.type)}
                </Badge>
              </div>
            </div>
            <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <span>Kode:</span>
                <code className="px-2 py-1 bg-muted rounded text-xs font-mono">
                  {supplier.code}
                </code>
                <CopyCodeButton code={supplier.code} />
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>Dibuat: {new Date(supplier.createdAt).toLocaleDateString('id-ID')}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Client-side interactive components */}
        <SupplierDetailClient supplier={supplier} onRefresh={refreshData} />
      </div>

      {/* Quick Statistics */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <Card className="border-l-4 border-l-orange-500">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-50 rounded-lg">
                <DollarSign className="h-5 w-5 text-orange-600" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-xs font-medium text-muted-foreground">
                  Saldo Tertunggak
                </p>
                <p className="text-lg font-bold text-orange-600 truncate">
                  {formatCurrency(totalOutstanding)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-50 rounded-lg">
                <FileText className="h-5 w-5 text-blue-600" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-xs font-medium text-muted-foreground">
                  Dokumen
                </p>
                <p className="text-lg font-bold text-blue-600">
                  {supplier._count?.documents || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-50 rounded-lg">
                <Calendar className="h-5 w-5 text-green-600" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-xs font-medium text-muted-foreground">
                  Total Transaksi
                </p>
                <p className="text-lg font-bold text-green-600">
                  {supplier._count?.payments || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-50 rounded-lg">
                <User className="h-5 w-5 text-purple-600" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-xs font-medium text-muted-foreground">
                  Kontak
                </p>
                <p className="text-lg font-bold text-purple-600">
                  {supplier.contacts?.length || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Sections */}
      <div className="grid gap-6 xl:grid-cols-3">
        {/* Left Column - Primary Information */}
        <div className="xl:col-span-2 space-y-6">
          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Phone className="h-5 w-5" />
                Informasi Kontak
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {supplier.phone && (
                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 text-muted-foreground shrink-0" />
                    <span className="text-sm break-all">{supplier.phone}</span>
                  </div>
                )}
                {supplier.email && (
                  <div className="flex items-center gap-3">
                    <Mail className="h-4 w-4 text-muted-foreground shrink-0" />
                    <span className="text-sm break-all">{supplier.email}</span>
                  </div>
                )}
                {supplier.website && (
                  <div className="flex items-center gap-3">
                    <Globe className="h-4 w-4 text-muted-foreground shrink-0" />
                    <a
                      href={supplier.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-blue-600 hover:underline break-all"
                    >
                      {supplier.website}
                    </a>
                  </div>
                )}

                {/* If no contact information is provided */}
                {!supplier.phone && !supplier.email && !supplier.website && (
                  <p className="text-sm text-muted-foreground">Informasi kontak belum diisi</p>
                )}
              </div>

              {/* Primary Contact */}
              {primaryContact && (
                <>
                  <Separator />
                  <div className="bg-muted/30 p-4 rounded-lg">
                    <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
                      <User className="h-4 w-4" />
                      Kontak Utama
                    </h4>
                    <div className="grid gap-3 sm:grid-cols-2">
                      <div>
                        <p className="font-medium text-sm">{primaryContact.name}</p>
                        {primaryContact.position && (
                          <p className="text-xs text-muted-foreground">{primaryContact.position}</p>
                        )}
                      </div>
                      <div className="space-y-1">
                        {primaryContact.phone && (
                          <div className="flex items-center gap-2 text-xs">
                            <Phone className="h-3 w-3" />
                            <span className="break-all">{primaryContact.phone}</span>
                          </div>
                        )}
                        {primaryContact.email && (
                          <div className="flex items-center gap-2 text-xs">
                            <Mail className="h-3 w-3" />
                            <span className="break-all">{primaryContact.email}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    {primaryContact.notes && (
                      <p className="text-xs text-muted-foreground mt-2">{primaryContact.notes}</p>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Address Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Informasi Alamat
              </CardTitle>
            </CardHeader>
            <CardContent>
              {supplier.address || supplier.city || supplier.province ? (
                <div className="space-y-3">
                  {supplier.address && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Alamat Lengkap</label>
                      <p className="text-sm mt-1 break-words">{supplier.address}</p>
                    </div>
                  )}
                  <div className="grid gap-3 sm:grid-cols-3">
                    {supplier.city && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Kota</label>
                        <p className="text-sm mt-1">{supplier.city}</p>
                      </div>
                    )}
                    {supplier.province && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Provinsi</label>
                        <p className="text-sm mt-1">{supplier.province}</p>
                      </div>
                    )}
                    {supplier.postalCode && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Kode Pos</label>
                        <p className="text-sm mt-1">{supplier.postalCode}</p>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">Alamat belum diisi</p>
              )}
            </CardContent>
          </Card>

          {/* Business Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Informasi Bisnis
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {supplier.npwp && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">NPWP</label>
                    <p className="text-sm mt-1 font-mono break-all">{supplier.npwp}</p>
                  </div>
                )}
                {supplier.licenseNumber && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Nomor Izin Usaha</label>
                    <p className="text-sm mt-1 break-words">{supplier.licenseNumber}</p>
                  </div>
                )}
                {supplier.pharmacyLicense && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Izin Apotek/PBF</label>
                    <p className="text-sm mt-1 break-words">{supplier.pharmacyLicense}</p>
                  </div>
                )}
              </div>
              {!supplier.npwp && !supplier.licenseNumber && !supplier.pharmacyLicense && (
                <p className="text-sm text-muted-foreground">Informasi bisnis belum diisi</p>
              )}
            </CardContent>
          </Card>

          {/* Financial Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Informasi Keuangan
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {supplier.creditLimit && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Limit Kredit</label>
                    <p className="text-sm mt-1 font-medium">
                      {formatCurrency(supplier.creditLimit)}
                    </p>
                  </div>
                )}
                {supplier.paymentTerms && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Termin Pembayaran</label>
                    <p className="text-sm mt-1">{supplier.paymentTerms} hari</p>
                  </div>
                )}
                {supplier.preferredPayment && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Metode Pembayaran</label>
                    <p className="text-sm mt-1">{getPaymentMethodLabel(supplier.preferredPayment)}</p>
                  </div>
                )}
              </div>

              {supplier.bankName && (
                <>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Informasi Bank</label>
                    <div className="mt-2 space-y-2">
                      <p className="text-sm font-medium">{supplier.bankName}</p>
                      {supplier.bankAccount && (
                        <div className="text-sm text-muted-foreground">
                          <span className="font-mono">{supplier.bankAccount}</span>
                          {supplier.bankAccountName && (
                            <span className="ml-2">({supplier.bankAccountName})</span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}

              {!supplier.creditLimit && !supplier.paymentTerms && !supplier.preferredPayment && !supplier.bankName && (
                <p className="text-sm text-muted-foreground">Informasi keuangan belum diisi</p>
              )}
            </CardContent>
          </Card>

          {/* All Contacts */}
          {supplier.contacts && supplier.contacts.length > 1 && (
            <Card>
              <CardHeader>
                <CardTitle>
                  Semua Kontak ({supplier.contacts.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 xl:grid-cols-2 2xl:grid-cols-3">
                  {supplier.contacts.map((contact) => (
                    <div key={contact.id} className="border rounded-lg p-4 hover:bg-muted/30 transition-colors">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-sm break-words">{contact.name}</h4>
                          {contact.position && (
                            <p className="text-xs text-muted-foreground break-words">{contact.position}</p>
                          )}
                        </div>
                        <div className="flex flex-col gap-1 ml-2">
                          {contact.isPrimary && (
                            <Badge variant="secondary" className="text-xs">
                              Utama
                            </Badge>
                          )}
                          <Badge
                            variant="outline"
                            className={`text-xs ${contact.isActive ? 'text-green-600' : 'text-gray-600'}`}
                          >
                            {contact.isActive ? 'Aktif' : 'Tidak Aktif'}
                          </Badge>
                        </div>
                      </div>
                      <div className="space-y-1">
                        {contact.phone && (
                          <div className="flex items-center gap-2 text-xs">
                            <Phone className="h-3 w-3 shrink-0" />
                            <span className="break-all">{contact.phone}</span>
                          </div>
                        )}
                        {contact.email && (
                          <div className="flex items-center gap-2 text-xs">
                            <Mail className="h-3 w-3 shrink-0" />
                            <span className="break-all">{contact.email}</span>
                          </div>
                        )}
                      </div>
                      {contact.notes && (
                        <p className="text-xs text-muted-foreground mt-2 break-words">{contact.notes}</p>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Column - Additional Information */}
        <div className="space-y-6">
          {/* System Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Informasi Sistem
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Dibuat</label>
                <p className="text-sm mt-1">
                  {new Date(supplier.createdAt).toLocaleString('id-ID')}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Diperbarui</label>
                <p className="text-sm mt-1">
                  {new Date(supplier.updatedAt).toLocaleString('id-ID')}
                </p>
              </div>
              {supplier.createdByUser && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Dibuat oleh</label>
                  <p className="text-sm mt-1">
                    {supplier.createdByUser.firstName} {supplier.createdByUser.lastName}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Notes Section */}
          {supplier.notes && (
            <Card>
              <CardHeader>
                <CardTitle>Catatan</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm whitespace-pre-wrap break-words">{supplier.notes}</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}