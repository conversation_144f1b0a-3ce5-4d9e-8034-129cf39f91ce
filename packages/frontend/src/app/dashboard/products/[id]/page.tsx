import { requireAuth } from '@/lib/server/auth';
import { createProductsApi } from '@/lib/server/products';
import { ProductDetailClient } from '@/components/products/ProductDetailClient';
import { notFound } from 'next/navigation';

interface ProductDetailPageProps {
  params: Promise<{ id: string }>;
}

export default async function ProductDetailPage({ params }: ProductDetailPageProps) {
  // Require authentication on server side
  const session = await requireAuth();

  // Await params before accessing properties (Next.js 15+ requirement)
  const resolvedParams = await params;
  const { id } = resolvedParams;

  try {
    // Fetch product data on server side
    const productsApi = createProductsApi(session);
    const product = await productsApi.getProduct(id);

    return <ProductDetailClient product={product} productId={id} />;
  } catch (error: any) {
    if (error?.response?.status === 404) {
      notFound();
    }
    throw error;
  }
}
