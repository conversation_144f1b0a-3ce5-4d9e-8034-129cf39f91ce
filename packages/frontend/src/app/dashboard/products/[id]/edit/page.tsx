import { requireAuth } from '@/lib/server/auth';
import { createProductsApi } from '@/lib/server/products';
import { ProductFormClient } from '@/components/products/ProductFormClient';
import { notFound } from 'next/navigation';

interface EditProductPageProps {
  params: Promise<{ id: string }>;
}

export default async function EditProductPage({ params }: EditProductPageProps) {
  // Require authentication on server side
  const session = await requireAuth();

  // Await params before accessing properties (Next.js 15+ requirement)
  const resolvedParams = await params;
  const { id } = resolvedParams;

  try {
    // Fetch product data on server side
    const productsApi = createProductsApi(session);
    const product = await productsApi.getProduct(id);

    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Edit Produk</h1>
          <p className="text-muted-foreground">
            Edit informasi produk {product.name}
          </p>
        </div>

        <ProductFormClient product={product} />
      </div>
    );
  } catch (error: any) {
    if (error?.response?.status === 404) {
      notFound();
    }
    throw error;
  }
}
