import { requireAuth } from '@/lib/server/auth';
import { ProductFormClient } from '@/components/products/ProductFormClient';

export default async function CreateProductPage() {
  // Require authentication on server side
  await requireAuth();

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Tambah Produk Baru</h1>
        <p className="text-muted-foreground">
          Tambahkan produk obat, alat kesehatan, atau produk farmasi lainnya
        </p>
      </div>

      <ProductFormClient />
    </div>
  );
}
