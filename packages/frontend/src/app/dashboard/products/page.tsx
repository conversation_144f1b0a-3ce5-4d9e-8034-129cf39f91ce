import { requireAuth } from '@/lib/server/auth';
import { ProductsPageClient } from '@/components/products/ProductsPageClient';
import { ProductStatsCards } from '@/components/products/ProductStatsCards';
import { ProductQueryParams } from '@/types/product';

interface ProductsPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function ProductsPage({ searchParams }: ProductsPageProps) {
  // Require authentication on server side
  const session = await requireAuth();

  // Await searchParams before accessing properties (Next.js 15+ requirement)
  const resolvedSearchParams = await searchParams;

  // Parse query parameters
  const query: ProductQueryParams = {
    page: resolvedSearchParams.page ? parseInt(resolvedSearchParams.page as string) : 1,
    limit: resolvedSearchParams.limit ? parseInt(resolvedSearchParams.limit as string) : 10,
    search: resolvedSearchParams.search as string || undefined,
    type: resolvedSearchParams.type as any || undefined,
    category: resolvedSearchParams.category as any || undefined,
    medicineClassification: resolvedSearchParams.medicineClassification as any || undefined,
    manufacturer: resolvedSearchParams.manufacturer as string || undefined,
    isActive: resolvedSearchParams.isActive ? resolvedSearchParams.isActive === 'true' : undefined,
    baseUnitId: resolvedSearchParams.baseUnitId as string || undefined,
    genericName: resolvedSearchParams.genericName as string || undefined,
    bpomNumber: resolvedSearchParams.bpomNumber as string || undefined,
    activeIngredient: resolvedSearchParams.activeIngredient as string || undefined,
    dosageForm: resolvedSearchParams.dosageForm as string || undefined,
    strength: resolvedSearchParams.strength as string || undefined,
    createdBy: resolvedSearchParams.createdBy as string || undefined,
    dateFrom: resolvedSearchParams.dateFrom as string || undefined,
    dateTo: resolvedSearchParams.dateTo as string || undefined,
    lowStock: resolvedSearchParams.lowStock ? resolvedSearchParams.lowStock === 'true' : undefined,
    outOfStock: resolvedSearchParams.outOfStock ? resolvedSearchParams.outOfStock === 'true' : undefined,
    overStock: resolvedSearchParams.overStock ? resolvedSearchParams.overStock === 'true' : undefined,
    sortBy: resolvedSearchParams.sortBy as string || 'createdAt',
    sortOrder: (resolvedSearchParams.sortOrder as 'asc' | 'desc') || 'desc',
  };

  return (
    <div className="space-y-8">
      {/* Statistics Cards */}
      <ProductStatsCards />
      
      {/* Products Management */}
      <ProductsPageClient initialQuery={query} />
    </div>
  );
}
