import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

export default function SalesLoading() {
  return (
    <div className="w-full min-w-0 max-w-full space-y-6 overflow-hidden">
      {/* Stats Cards Loading */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Header Loading */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <Skeleton className="h-8 w-48 mb-2" />
          <Skeleton className="h-4 w-64" />
        </div>
        <div className="flex items-center gap-2">
          <Skeleton className="h-10 w-20" />
          <Skeleton className="h-10 w-20" />
          <Skeleton className="h-10 w-32" />
        </div>
      </div>

      {/* Table Loading */}
      <Card className="w-full min-w-0 max-w-full overflow-hidden">
        <CardContent className="p-0 sm:p-6">
          <div className="w-full min-w-0 max-w-full space-y-4">
            {/* Search and Filters Loading */}
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
              <div className="flex flex-1 items-center space-x-2">
                <Skeleton className="h-10 w-64" />
                <Skeleton className="h-10 w-32" />
                <Skeleton className="h-10 w-32" />
                <Skeleton className="h-10 w-32" />
              </div>
              <div className="flex items-center space-x-2">
                <Skeleton className="h-10 w-20" />
              </div>
            </div>

            {/* Table Header Loading */}
            <div className="rounded-md border">
              <div className="border-b">
                <div className="flex items-center h-12 px-4">
                  {Array.from({ length: 8 }).map((_, index) => (
                    <div key={index} className="flex-1 px-2">
                      <Skeleton className="h-4 w-full" />
                    </div>
                  ))}
                </div>
              </div>

              {/* Table Rows Loading */}
              {Array.from({ length: 10 }).map((_, index) => (
                <div key={index} className="border-b last:border-b-0">
                  <div className="flex items-center h-16 px-4">
                    {Array.from({ length: 8 }).map((_, cellIndex) => (
                      <div key={cellIndex} className="flex-1 px-2">
                        <Skeleton className="h-4 w-full" />
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination Loading */}
            <div className="flex items-center justify-between space-x-2 py-4">
              <div className="flex items-center space-x-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-8 w-16" />
              </div>
              <div className="flex items-center space-x-6 lg:space-x-8">
                <Skeleton className="h-4 w-24" />
                <div className="flex items-center space-x-2">
                  <Skeleton className="h-8 w-8" />
                  <Skeleton className="h-8 w-8" />
                  <Skeleton className="h-8 w-8" />
                  <Skeleton className="h-8 w-8" />
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
