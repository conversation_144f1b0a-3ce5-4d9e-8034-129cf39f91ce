import { requireAuth } from '@/lib/server/auth';
import { SiteHeader } from '@/components/site-header';
import { CreateSaleFormClient } from '@/components/sales/CreateSaleFormClient';

export default async function CreateSalePage() {
  // Require authentication on server side
  const session = await requireAuth();

  return (
    <>
      <SiteHeader user={session.user} />
      <div className="@container/main h-(--content-height) overflow-y-auto">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="px-4 lg:px-6">
            <div>
              <h1 className="text-2xl font-bold tracking-tight">Transaksi Baru</h1>
              <p className="text-muted-foreground">
                Buat transaksi penjualan baru dengan memilih pelanggan, produk, dan metode pembayaran
              </p>
            </div>
          </div>

          <div className="px-4 lg:px-6">
            <CreateSaleFormClient />
          </div>
        </div>
      </div>
    </>
  );
}
