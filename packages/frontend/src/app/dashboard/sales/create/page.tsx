import { requireAuth } from '@/lib/server/auth';
import { CreateSaleFormClient } from '@/components/sales/CreateSaleFormClient';

export default async function CreateSalePage() {
  // Require authentication on server side
  await requireAuth();

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Transaksi Baru</h1>
        <p className="text-muted-foreground">
          Buat transaksi penjualan baru dengan memilih pelanggan, produk, dan metode pembayaran
        </p>
      </div>

      <CreateSaleFormClient />
    </div>
  );
}
