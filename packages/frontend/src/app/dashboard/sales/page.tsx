import { requireAuth } from '@/lib/server/auth';
import { SalesPageClient } from '@/components/sales/SalesPageClient';
import { SalesStatsCards } from '@/components/sales/SalesStatsCards';
import { SaleQueryParams } from '@/types/sales';

interface SalesPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function SalesPage({ searchParams }: SalesPageProps) {
  // Require authentication on server side
  const session = await requireAuth();

  // Await searchParams before accessing properties (Next.js 15+ requirement)
  const resolvedSearchParams = await searchParams;

  // Parse query parameters
  const filters: SaleQueryParams = {
    page: resolvedSearchParams.page ? parseInt(resolvedSearchParams.page as string) : 1,
    limit: resolvedSearchParams.limit ? parseInt(resolvedSearchParams.limit as string) : 10,
    search: resolvedSearchParams.search as string | undefined,
    status: resolvedSearchParams.status as any,
    paymentMethod: resolvedSearchParams.paymentMethod as any,
    customerId: resolvedSearchParams.customerId as string | undefined,
    cashierId: resolvedSearchParams.cashierId as string | undefined,
    startDate: resolvedSearchParams.startDate as string | undefined,
    endDate: resolvedSearchParams.endDate as string | undefined,
    sortBy: (resolvedSearchParams.sortBy as string) || 'createdAt',
    sortOrder: (resolvedSearchParams.sortOrder as 'asc' | 'desc') || 'desc',
  };

  return (
    <div className="w-full min-w-0 max-w-full space-y-6 overflow-hidden">
      {/* Stats Cards */}
      <SalesStatsCards />

      {/* Client-side interactive components */}
      <SalesPageClient
        initialQuery={filters}
      />
    </div>
  );
}
